"""
Notification Service for Daily Bonus Reminders
Handles automated daily bonus reminder notifications with batch processing and error handling
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Optional, Any
import pytz
from motor.motor_asyncio import AsyncIOMotorDatabase
from telegram import Bo<PERSON>
from telegram.error import TelegramError, Forbidden, BadRequest, RetryAfter

from config import Config
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

class NotificationService:
    """Service for managing daily bonus reminder notifications"""

    def __init__(self, db: AsyncIOMotorDatabase, bot: Bot):
        self.db = db
        self.bot = bot
        self.users_collection = db.users
        self.notification_logs_collection = db.notification_logs

        # Batch processing settings - COMPLIANT with Telegram rate limits
        self.batch_size = 1000  # Process users in larger batches for efficiency
        self.delay_between_batches = 1.0  # 1 second delay between batches
        self.delay_between_messages = 0.034  # 34ms delay = ~29 messages/second (under 30/sec limit)
        self.concurrent_batches = 1  # Sequential processing to respect rate limits

        # Rate limiting settings
        self.max_messages_per_second = 29  # Stay under Telegram's 30/sec limit
        self.rate_limit_window = 1.0  # 1 second window for rate limiting

        # IST timezone for scheduling
        self.ist_tz = pytz.timezone('Asia/Kolkata')

    async def toggle_user_notifications(self, user_id: int) -> Dict[str, Any]:
        """Toggle daily notifications for a user"""
        try:
            # Get current user data
            user_data = await self.users_collection.find_one({'user_id': user_id})
            if not user_data:
                return {
                    'success': False,
                    'error': 'User not found',
                    'enabled': False
                }

            # Get current notification status (default to True if not set)
            current_status = user_data.get('daily_notifications', True)
            new_status = not current_status

            # Update user's notification preference
            result = await self.users_collection.update_one(
                {'user_id': user_id},
                {
                    '$set': {
                        'daily_notifications': new_status,
                        'updated_at': datetime.now(timezone.utc)
                    }
                }
            )

            if result.modified_count > 0:
                logger.info(f"User {user_id} notifications toggled: {current_status} -> {new_status}")
                return {
                    'success': True,
                    'enabled': new_status,
                    'previous_status': current_status
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to update notification preference',
                    'enabled': current_status
                }

        except Exception as e:
            logger.error(f"Error toggling notifications for user {user_id}: {e}")
            return {
                'success': False,
                'error': str(e),
                'enabled': False
            }

    async def get_user_notification_status(self, user_id: int) -> bool:
        """Get user's current notification status"""
        try:
            user_data = await self.users_collection.find_one(
                {'user_id': user_id},
                {'daily_notifications': 1}
            )

            if user_data:
                return user_data.get('daily_notifications', True)
            return True  # Default to enabled for new users

        except Exception as e:
            logger.error(f"Error getting notification status for user {user_id}: {e}")
            return True  # Default to enabled on error

    async def get_users_with_notifications_enabled(self, batch_size: Optional[int] = None) -> List[int]:
        """Get list of user IDs who have notifications enabled - optimized for large datasets"""
        try:
            if batch_size is None:
                batch_size = self.batch_size

            # Query users with notifications enabled and active accounts
            # Use projection to only fetch user_id for memory efficiency
            cursor = self.users_collection.find(
                {
                    'daily_notifications': True,
                    'is_active': True,
                    'is_banned': False,
                    'has_joined_channels': True  # Only notify users who completed registration
                },
                {'user_id': 1, '_id': 0}  # Only fetch user_id, exclude _id for efficiency
            ).batch_size(10000)  # Use larger batch size for cursor efficiency

            user_ids = []
            batch_count = 0

            async for user_doc in cursor:
                user_ids.append(user_doc['user_id'])

                # Log progress for large datasets
                if len(user_ids) % 50000 == 0:
                    logger.info(f"📊 Loaded {len(user_ids)} user IDs so far...")

            logger.info(f"Found {len(user_ids)} users with notifications enabled")

            # Log memory usage warning for very large datasets
            if len(user_ids) > 500000:
                logger.warning(f"⚠️ Large dataset detected: {len(user_ids)} users. Consider using streaming approach for >1M users.")

            return user_ids

        except Exception as e:
            logger.error(f"Error getting users with notifications enabled: {e}")
            return []

    async def send_daily_reminder(self, user_id: int, retry_count: int = 0) -> Dict[str, Any]:
        """Send daily bonus reminder to a single user with rate limiting and retry logic"""
        try:
            # Get current time in IST
            ist_now = datetime.now(self.ist_tz)

            # Create reminder message with user's aesthetic preferences
            reminder_text = f"""
╭─────────────────────────╮
│  ✧ **DAILY GIFT** ✧  │
╰─────────────────────────╯

🎁 **Your daily gift is ready!**

*Claim your free diamonds now and keep your streak going!*

💎 **Tap "✧ Daily Gift ✧" to claim**

━━━━━━━━━━━━━━━━━━━━━━━━━━━━

💡 *To turn notifications on/off, use /notification command*
"""

            # Send the reminder with rate limiting compliance
            await self.bot.send_message(
                chat_id=user_id,
                text=reminder_text,
                parse_mode='Markdown'
            )

            # Log successful notification
            await self._log_notification(user_id, 'sent', 'Daily reminder sent successfully')

            return {
                'success': True,
                'user_id': user_id,
                'sent_at': ist_now.isoformat()
            }

        except RetryAfter as e:
            # Handle 429 rate limit errors
            retry_after = e.retry_after
            logger.warning(f"Rate limit hit for user {user_id}, waiting {retry_after} seconds")

            if retry_count < 3:  # Max 3 retries
                await asyncio.sleep(retry_after + 1)  # Wait the required time + 1 second buffer
                return await self.send_daily_reminder(user_id, retry_count + 1)
            else:
                await self._log_notification(user_id, 'failed', f'Rate limit exceeded after {retry_count} retries')
                return {
                    'success': False,
                    'user_id': user_id,
                    'error': 'rate_limit_exceeded',
                    'retry_after': retry_after
                }

        except Forbidden as e:
            # User blocked the bot
            logger.info(f"User {user_id} blocked the bot, cleaning up user data")
            await self._cleanup_user_data(user_id, 'bot_blocked')
            return {
                'success': False,
                'user_id': user_id,
                'error': 'bot_blocked',
                'cleanup_performed': True
            }

        except BadRequest as e:
            # Chat not found or user deleted account
            if 'chat not found' in str(e).lower() or 'user not found' in str(e).lower():
                logger.info(f"User {user_id} account deleted or chat not found, cleaning up user data")
                await self._cleanup_user_data(user_id, 'account_deleted')
                return {
                    'success': False,
                    'user_id': user_id,
                    'error': 'account_deleted',
                    'cleanup_performed': True
                }
            else:
                logger.error(f"BadRequest error sending reminder to user {user_id}: {e}")
                await self._log_notification(user_id, 'failed', f'BadRequest: {str(e)}')
                return {
                    'success': False,
                    'user_id': user_id,
                    'error': str(e)
                }

        except TelegramError as e:
            logger.error(f"Telegram error sending reminder to user {user_id}: {e}")
            await self._log_notification(user_id, 'failed', f'TelegramError: {str(e)}')
            return {
                'success': False,
                'user_id': user_id,
                'error': str(e)
            }

        except Exception as e:
            logger.error(f"Unexpected error sending reminder to user {user_id}: {e}")
            await self._log_notification(user_id, 'failed', f'Unexpected error: {str(e)}')
            return {
                'success': False,
                'user_id': user_id,
                'error': str(e)
            }

    async def send_batch_reminders(self, user_ids: List[int]) -> Dict[str, Any]:
        """Send daily reminders to a batch of users with rate limiting"""
        try:
            results = {
                'total_users': len(user_ids),
                'sent_count': 0,
                'failed_count': 0,
                'cleanup_count': 0,
                'errors': []
            }

            logger.info(f"Sending daily reminders to {len(user_ids)} users...")

            for i, user_id in enumerate(user_ids):
                try:
                    result = await self.send_daily_reminder(user_id)

                    if result['success']:
                        results['sent_count'] += 1
                    else:
                        results['failed_count'] += 1
                        if result.get('cleanup_performed'):
                            results['cleanup_count'] += 1
                        results['errors'].append({
                            'user_id': user_id,
                            'error': result.get('error', 'Unknown error')
                        })

                    # Rate limiting delay between messages
                    if i < len(user_ids) - 1:  # Don't delay after the last message
                        await asyncio.sleep(self.delay_between_messages)

                except Exception as e:
                    logger.error(f"Error processing user {user_id} in batch: {e}")
                    results['failed_count'] += 1
                    results['errors'].append({
                        'user_id': user_id,
                        'error': str(e)
                    })

            logger.info(f"Batch complete: {results['sent_count']} sent, {results['failed_count']} failed, {results['cleanup_count']} cleaned up")
            return results

        except Exception as e:
            logger.error(f"Error in batch reminder sending: {e}")
            return {
                'total_users': len(user_ids),
                'sent_count': 0,
                'failed_count': len(user_ids),
                'cleanup_count': 0,
                'errors': [{'error': str(e)}]
            }

    async def _cleanup_user_data(self, user_id: int, reason: str):
        """Remove user data when notifications fail due to blocked bot or deleted account"""
        try:
            # Delete user from all collections
            collections_to_clean = [
                'users',
                'transactions',
                'referrals',
                'withdrawals',
                'purchase_requests',
                'user_tasks',
                'task_submissions',
                'notification_logs'  # Also clean notification logs for this user
            ]

            cleanup_results = {}
            total_deleted = 0

            for collection_name in collections_to_clean:
                try:
                    collection = getattr(self.db, collection_name)
                    result = await collection.delete_many({'user_id': user_id})
                    cleanup_results[collection_name] = result.deleted_count
                    total_deleted += result.deleted_count
                except Exception as e:
                    logger.error(f"Error cleaning {collection_name} for user {user_id}: {e}")
                    cleanup_results[collection_name] = 0

            # Log the cleanup action (before cleaning notification_logs)
            await self._log_notification(
                user_id,
                'cleanup',
                f'User data cleaned up due to: {reason}. Total deleted: {total_deleted} records. Details: {cleanup_results}'
            )

            logger.info(f"🧹 Cleaned up user {user_id} data due to {reason}: {total_deleted} total records deleted")
            logger.debug(f"Cleanup details for user {user_id}: {cleanup_results}")

            return {
                'success': True,
                'total_deleted': total_deleted,
                'details': cleanup_results,
                'reason': reason
            }

        except Exception as e:
            logger.error(f"Error cleaning up user {user_id} data: {e}")
            return {
                'success': False,
                'error': str(e),
                'total_deleted': 0
            }

    async def cleanup_inactive_users(self, days_inactive: int = 30) -> Dict[str, Any]:
        """Clean up users who haven't been active for specified days and have blocked the bot"""
        try:
            logger.info(f"🧹 Starting cleanup of users inactive for {days_inactive} days...")

            # Calculate cutoff date
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_inactive)

            # Find users who haven't been active and might have blocked the bot
            # We'll identify them by failed notification attempts
            failed_users_cursor = self.db.notification_logs.aggregate([
                {
                    '$match': {
                        'status': 'failed',
                        'timestamp': {'$gte': cutoff_date}
                    }
                },
                {
                    '$group': {
                        '_id': '$user_id',
                        'failed_count': {'$sum': 1},
                        'last_failed': {'$max': '$timestamp'}
                    }
                },
                {
                    '$match': {
                        'failed_count': {'$gte': 3}  # 3 or more failed attempts
                    }
                }
            ])

            cleanup_results = {
                'total_checked': 0,
                'total_cleaned': 0,
                'errors': []
            }

            async for user_doc in failed_users_cursor:
                user_id = user_doc['_id']
                failed_count = user_doc['failed_count']

                cleanup_results['total_checked'] += 1

                try:
                    # Attempt to send a test message to verify if user blocked the bot
                    test_result = await self.send_daily_reminder(user_id)

                    if not test_result['success'] and test_result.get('cleanup_performed'):
                        cleanup_results['total_cleaned'] += 1
                        logger.info(f"Cleaned up inactive user {user_id} with {failed_count} failed notifications")

                except Exception as e:
                    cleanup_results['errors'].append({
                        'user_id': user_id,
                        'error': str(e)
                    })
                    logger.error(f"Error checking user {user_id} during cleanup: {e}")

                # Small delay to avoid overwhelming the system
                await asyncio.sleep(0.1)

            logger.info(f"✅ Inactive user cleanup complete: {cleanup_results['total_cleaned']} users cleaned")
            return cleanup_results

        except Exception as e:
            logger.error(f"Error in inactive user cleanup: {e}")
            return {
                'total_checked': 0,
                'total_cleaned': 0,
                'errors': [{'error': str(e)}]
            }

    async def send_daily_reminders_streaming(self) -> Dict[str, Any]:
        """Memory-efficient streaming approach for 1M+ users - processes users without loading all into memory"""
        try:
            start_time = datetime.now(timezone.utc)
            logger.info("🔔 Starting daily reminder broadcast (streaming mode for large datasets)...")

            # Initialize results
            total_results = {
                'total_users': 0,
                'sent_count': 0,
                'failed_count': 0,
                'cleanup_count': 0,
                'errors': []
            }

            # Create cursor for streaming users
            cursor = self.users_collection.find(
                {
                    'daily_notifications': True,
                    'is_active': True,
                    'is_banned': False,
                    'has_joined_channels': True
                },
                {'user_id': 1, '_id': 0}
            ).batch_size(self.batch_size)

            # Process users in streaming batches (sequential for rate limiting)
            current_batch = []
            batch_num = 0

            async def process_streaming_batch(batch_users: List[int], batch_number: int):
                logger.info(f"Processing streaming batch {batch_number} ({len(batch_users)} users)")
                batch_results = await self.send_batch_reminders(batch_users)

                # Aggregate results
                total_results['sent_count'] += batch_results['sent_count']
                total_results['failed_count'] += batch_results['failed_count']
                total_results['cleanup_count'] += batch_results['cleanup_count']
                total_results['errors'].extend(batch_results['errors'])

                await asyncio.sleep(self.delay_between_batches)
                return batch_results

            # Stream and process users
            async for user_doc in cursor:
                current_batch.append(user_doc['user_id'])
                total_results['total_users'] += 1

                # When batch is full, process it sequentially
                if len(current_batch) >= self.batch_size:
                    batch_num += 1

                    # Process batch sequentially to respect rate limits
                    await process_streaming_batch(current_batch.copy(), batch_num)
                    current_batch.clear()

                    # Log progress every 10 batches
                    if batch_num % 10 == 0:
                        logger.info(f"📊 Streaming progress: {total_results['total_users']} users processed, {batch_num} batches sent")

            # Process remaining users in final batch
            if current_batch:
                batch_num += 1
                await process_streaming_batch(current_batch, batch_num)

            end_time = datetime.now(timezone.utc)
            duration = (end_time - start_time).total_seconds()

            logger.info(f"✅ Streaming daily reminder broadcast complete in {duration:.2f}s")
            logger.info(f"📊 Final results: {total_results['sent_count']} sent, {total_results['failed_count']} failed, {total_results['cleanup_count']} cleaned up")

            # Log broadcast summary
            await self._log_broadcast_summary(total_results, duration)

            return {
                'success': True,
                'duration_seconds': duration,
                **total_results
            }

        except Exception as e:
            logger.error(f"Error in streaming daily reminder broadcast: {e}")
            return {
                'success': False,
                'error': str(e),
                'total_users': 0,
                'sent_count': 0,
                'failed_count': 0,
                'cleanup_count': 0,
                'duration_seconds': 0
            }

    async def _log_notification(self, user_id: int, status: str, details: str):
        """Log notification attempt for monitoring"""
        try:
            log_entry = {
                'user_id': user_id,
                'status': status,  # 'sent', 'failed', 'cleanup'
                'details': details,
                'timestamp': datetime.now(timezone.utc),
                'ist_timestamp': datetime.now(self.ist_tz).isoformat()
            }

            await self.notification_logs_collection.insert_one(log_entry)

        except Exception as e:
            logger.error(f"Error logging notification for user {user_id}: {e}")

    async def send_daily_reminders_to_all(self) -> Dict[str, Any]:
        """Send daily reminders to all users with notifications enabled using batch processing"""
        try:
            start_time = datetime.now(timezone.utc)
            logger.info("🔔 Starting daily reminder broadcast...")

            # Get all users with notifications enabled
            user_ids = await self.get_users_with_notifications_enabled()

            if not user_ids:
                logger.info("No users with notifications enabled found")
                return {
                    'success': True,
                    'total_users': 0,
                    'sent_count': 0,
                    'failed_count': 0,
                    'cleanup_count': 0,
                    'duration_seconds': 0
                }

            # Process users in batches for better performance
            total_results = {
                'total_users': len(user_ids),
                'sent_count': 0,
                'failed_count': 0,
                'cleanup_count': 0,
                'errors': []
            }

            # Split users into batches
            batches = [user_ids[i:i + self.batch_size] for i in range(0, len(user_ids), self.batch_size)]
            logger.info(f"Processing {len(user_ids)} users in {len(batches)} batches of {self.batch_size}")
            logger.info(f"Using sequential processing to respect Telegram's 30 messages/second rate limit")

            # Process batches sequentially to respect rate limits
            await self._process_batches_sequentially(batches, total_results)

            end_time = datetime.now(timezone.utc)
            duration = (end_time - start_time).total_seconds()

            logger.info(f"✅ Daily reminder broadcast complete in {duration:.2f}s")
            logger.info(f"📊 Results: {total_results['sent_count']} sent, {total_results['failed_count']} failed, {total_results['cleanup_count']} cleaned up")

            # Log broadcast summary
            await self._log_broadcast_summary(total_results, duration)

            return {
                'success': True,
                'duration_seconds': duration,
                **total_results
            }

        except Exception as e:
            logger.error(f"Error in daily reminder broadcast: {e}")
            return {
                'success': False,
                'error': str(e),
                'total_users': 0,
                'sent_count': 0,
                'failed_count': 0,
                'cleanup_count': 0,
                'duration_seconds': 0
            }

    async def _process_batches_sequentially(self, batches: List[List[int]], total_results: Dict[str, Any]):
        """Process batches sequentially to respect Telegram rate limits"""
        try:
            logger.info(f"Processing {len(batches)} batches sequentially to respect Telegram rate limits")

            for batch_num, batch_user_ids in enumerate(batches, 1):
                logger.info(f"Processing batch {batch_num}/{len(batches)} ({len(batch_user_ids)} users)")

                batch_results = await self.send_batch_reminders(batch_user_ids)

                # Aggregate results
                total_results['sent_count'] += batch_results['sent_count']
                total_results['failed_count'] += batch_results['failed_count']
                total_results['cleanup_count'] += batch_results['cleanup_count']
                total_results['errors'].extend(batch_results['errors'])

                # Log progress every 10 batches
                if batch_num % 10 == 0:
                    progress_percent = (batch_num / len(batches)) * 100
                    logger.info(f"📊 Progress: {batch_num}/{len(batches)} batches completed ({progress_percent:.1f}%)")
                    logger.info(f"📈 Current stats: {total_results['sent_count']} sent, {total_results['failed_count']} failed")

                # Delay between batches to respect rate limits
                if batch_num < len(batches):
                    await asyncio.sleep(self.delay_between_batches)

            logger.info(f"✅ All {len(batches)} batches processed sequentially")

        except Exception as e:
            logger.error(f"Error in sequential batch processing: {e}")
            raise

    async def _log_broadcast_summary(self, results: Dict[str, Any], duration: float):
        """Log broadcast summary for monitoring"""
        try:
            summary = {
                'type': 'daily_reminder_broadcast',
                'timestamp': datetime.now(timezone.utc),
                'ist_timestamp': datetime.now(self.ist_tz).isoformat(),
                'duration_seconds': duration,
                'results': results
            }

            await self.notification_logs_collection.insert_one(summary)

        except Exception as e:
            logger.error(f"Error logging broadcast summary: {e}")

    async def get_notification_statistics(self) -> Dict[str, Any]:
        """Get notification system statistics"""
        try:
            # Count users by notification preference
            total_users = await self.users_collection.count_documents({'is_active': True})
            notifications_enabled = await self.users_collection.count_documents({
                'daily_notifications': True,
                'is_active': True
            })
            notifications_disabled = await self.users_collection.count_documents({
                'daily_notifications': False,
                'is_active': True
            })

            # Get recent notification logs (last 24 hours)
            yesterday = datetime.now(timezone.utc) - timedelta(days=1)
            recent_logs = await self.notification_logs_collection.count_documents({
                'timestamp': {'$gte': yesterday}
            })

            # Get recent successful notifications
            recent_sent = await self.notification_logs_collection.count_documents({
                'timestamp': {'$gte': yesterday},
                'status': 'sent'
            })

            # Get recent cleanup actions
            recent_cleanup = await self.notification_logs_collection.count_documents({
                'timestamp': {'$gte': yesterday},
                'status': 'cleanup'
            })

            return {
                'total_active_users': total_users,
                'notifications_enabled': notifications_enabled,
                'notifications_disabled': notifications_disabled,
                'enabled_percentage': round((notifications_enabled / total_users * 100) if total_users > 0 else 0, 2),
                'last_24h_logs': recent_logs,
                'last_24h_sent': recent_sent,
                'last_24h_cleanup': recent_cleanup
            }

        except Exception as e:
            logger.error(f"Error getting notification statistics: {e}")
            return {
                'total_active_users': 0,
                'notifications_enabled': 0,
                'notifications_disabled': 0,
                'enabled_percentage': 0,
                'last_24h_logs': 0,
                'last_24h_sent': 0,
                'last_24h_cleanup': 0
            }