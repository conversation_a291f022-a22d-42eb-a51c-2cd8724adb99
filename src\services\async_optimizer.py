"""
Async Optimization Service for High-Concurrency Operations
Handles batch processing, concurrent operations, and queue management
"""

import asyncio
import logging
from typing import List, Dict, Any, Callable, Optional, Union
from datetime import datetime, timezone
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class TaskPriority(Enum):
    """Task priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class AsyncTask:
    """Async task wrapper"""
    id: str
    func: Callable
    args: tuple
    kwargs: dict
    priority: TaskPriority = TaskPriority.NORMAL
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now(timezone.utc)

class AsyncOptimizer:
    """High-performance async operations manager"""
    
    def __init__(self, max_concurrent_tasks: int = 100):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.task_queue: asyncio.Queue = asyncio.Queue()
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.completed_tasks: Dict[str, Any] = {}
        self.failed_tasks: Dict[str, Exception] = {}
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'concurrent_tasks': 0
        }
        self._worker_tasks: List[asyncio.Task] = []
        self._running = False
    
    async def start(self):
        """Start the async optimizer workers"""
        if self._running:
            return
        
        self._running = True
        # Start worker tasks
        for i in range(min(10, self.max_concurrent_tasks)):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self._worker_tasks.append(worker)
        
        logger.info(f"AsyncOptimizer started with {len(self._worker_tasks)} workers")
    
    async def stop(self):
        """Stop the async optimizer"""
        self._running = False
        
        # Cancel all worker tasks
        for worker in self._worker_tasks:
            worker.cancel()
        
        # Wait for workers to finish
        await asyncio.gather(*self._worker_tasks, return_exceptions=True)
        self._worker_tasks.clear()
        
        logger.info("AsyncOptimizer stopped")
    
    async def submit_task(self, task_id: str, func: Callable, *args, 
                         priority: TaskPriority = TaskPriority.NORMAL, **kwargs) -> str:
        """Submit a task for async execution"""
        task = AsyncTask(
            id=task_id,
            func=func,
            args=args,
            kwargs=kwargs,
            priority=priority
        )
        
        await self.task_queue.put(task)
        self.stats['total_tasks'] += 1
        
        logger.debug(f"Task {task_id} submitted with priority {priority.name}")
        return task_id
    
    async def get_result(self, task_id: str, timeout: Optional[float] = None) -> Any:
        """Get task result (blocking until completion)"""
        start_time = asyncio.get_event_loop().time()
        
        while True:
            # Check if task completed
            if task_id in self.completed_tasks:
                return self.completed_tasks.pop(task_id)
            
            # Check if task failed
            if task_id in self.failed_tasks:
                error = self.failed_tasks.pop(task_id)
                raise error
            
            # Check timeout
            if timeout and (asyncio.get_event_loop().time() - start_time) > timeout:
                raise asyncio.TimeoutError(f"Task {task_id} timed out after {timeout} seconds")
            
            # Wait a bit before checking again
            await asyncio.sleep(0.01)
    
    async def batch_execute(self, tasks: List[Dict[str, Any]], 
                           max_concurrent: int = 50) -> List[Any]:
        """Execute multiple tasks concurrently with controlled concurrency"""
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def execute_with_semaphore(task_data):
            async with semaphore:
                func = task_data['func']
                args = task_data.get('args', ())
                kwargs = task_data.get('kwargs', {})
                
                try:
                    if asyncio.iscoroutinefunction(func):
                        return await func(*args, **kwargs)
                    else:
                        return func(*args, **kwargs)
                except Exception as e:
                    logger.error(f"Batch task failed: {e}")
                    return None
        
        # Execute all tasks concurrently
        results = await asyncio.gather(
            *[execute_with_semaphore(task) for task in tasks],
            return_exceptions=True
        )
        
        return results
    
    async def batch_database_operations(self, operations: List[Dict[str, Any]]) -> List[Any]:
        """Execute database operations in optimized batches"""
        # Group operations by type for better performance
        grouped_ops = {}
        for op in operations:
            op_type = op.get('type', 'unknown')
            if op_type not in grouped_ops:
                grouped_ops[op_type] = []
            grouped_ops[op_type].append(op)
        
        results = []
        
        # Execute each group concurrently
        for op_type, ops in grouped_ops.items():
            if op_type == 'insert':
                result = await self._batch_insert(ops)
            elif op_type == 'update':
                result = await self._batch_update(ops)
            elif op_type == 'delete':
                result = await self._batch_delete(ops)
            else:
                result = await self.batch_execute(ops, max_concurrent=20)
            
            results.extend(result if isinstance(result, list) else [result])
        
        return results
    
    async def _worker(self, worker_name: str):
        """Worker task that processes the task queue"""
        logger.debug(f"Worker {worker_name} started")
        
        while self._running:
            try:
                # Get task from queue with timeout
                task = await asyncio.wait_for(self.task_queue.get(), timeout=1.0)
                
                # Execute task
                self.stats['concurrent_tasks'] += 1
                self.running_tasks[task.id] = asyncio.current_task()
                
                try:
                    if asyncio.iscoroutinefunction(task.func):
                        result = await task.func(*task.args, **task.kwargs)
                    else:
                        result = task.func(*task.args, **task.kwargs)
                    
                    self.completed_tasks[task.id] = result
                    self.stats['completed_tasks'] += 1
                    
                except Exception as e:
                    self.failed_tasks[task.id] = e
                    self.stats['failed_tasks'] += 1
                    logger.error(f"Task {task.id} failed: {e}")
                
                finally:
                    self.running_tasks.pop(task.id, None)
                    self.stats['concurrent_tasks'] -= 1
                    self.task_queue.task_done()
                
            except asyncio.TimeoutError:
                # No tasks in queue, continue
                continue
            except asyncio.CancelledError:
                # Worker cancelled
                break
            except Exception as e:
                logger.error(f"Worker {worker_name} error: {e}")
        
        logger.debug(f"Worker {worker_name} stopped")
    
    async def _batch_insert(self, operations: List[Dict[str, Any]]) -> List[Any]:
        """Optimized batch insert operations"""
        # Group by collection
        collections = {}
        for op in operations:
            collection = op.get('collection')
            if collection not in collections:
                collections[collection] = []
            collections[collection].append(op.get('data'))
        
        results = []
        for collection, data_list in collections.items():
            try:
                # Perform bulk insert
                result = await collection.insert_many(data_list, ordered=False)
                results.append(result)
            except Exception as e:
                logger.error(f"Batch insert failed for collection: {e}")
                results.append(None)
        
        return results
    
    async def _batch_update(self, operations: List[Dict[str, Any]]) -> List[Any]:
        """Optimized batch update operations"""
        # Execute updates concurrently
        tasks = []
        for op in operations:
            collection = op.get('collection')
            filter_doc = op.get('filter')
            update_doc = op.get('update')
            
            task = collection.update_one(filter_doc, update_doc)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return results
    
    async def _batch_delete(self, operations: List[Dict[str, Any]]) -> List[Any]:
        """Optimized batch delete operations"""
        # Execute deletes concurrently
        tasks = []
        for op in operations:
            collection = op.get('collection')
            filter_doc = op.get('filter')
            
            task = collection.delete_many(filter_doc)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return results
    
    def get_stats(self) -> Dict[str, Any]:
        """Get optimizer statistics"""
        return {
            **self.stats,
            'queue_size': self.task_queue.qsize(),
            'running_tasks': len(self.running_tasks),
            'workers_active': len(self._worker_tasks),
            'is_running': self._running
        }

# Global optimizer instance
async_optimizer = None

def get_async_optimizer() -> AsyncOptimizer:
    """Get global async optimizer instance"""
    global async_optimizer
    if async_optimizer is None:
        async_optimizer = AsyncOptimizer(max_concurrent_tasks=100)
    return async_optimizer
