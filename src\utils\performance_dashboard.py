"""
Performance Dashboard for Telegram Bot
Real-time monitoring of callback queries, timeouts, and system performance
"""

import asyncio
import logging
import time
from typing import Dict, Any, List
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class PerformanceSummary:
    """Performance summary data structure"""
    timestamp: datetime
    callback_queries_total: int
    callback_queries_successful: int
    callback_queries_timeout: int
    callback_queries_rate_limited: int
    average_response_time_ms: float
    concurrent_users: int
    system_health_score: int
    alerts: List[str]

class PerformanceDashboard:
    """Real-time performance monitoring dashboard"""
    
    def __init__(self, callback_manager=None, performance_monitor=None, concurrent_ops=None):
        self.callback_manager = callback_manager
        self.performance_monitor = performance_monitor
        self.concurrent_ops = concurrent_ops
        
        # Dashboard settings
        self.update_interval = 30  # Update every 30 seconds
        self.history_retention_hours = 24  # Keep 24 hours of history
        
        # Performance history
        self.performance_history: List[PerformanceSummary] = []
        
        # Alert thresholds - Optimized for MongoDB Atlas
        self.alert_thresholds = {
            'callback_timeout_rate': 15.0,  # 15% timeout rate (more lenient)
            'average_response_time': 8000.0,  # 8 seconds (realistic for MongoDB Atlas)
            'concurrent_users': 500,  # 500 concurrent users
            'system_health': 60  # Health score below 60 (more lenient)
        }
        
        # Dashboard state
        self._running = False
        self._dashboard_task = None
    
    async def start(self):
        """Start the performance dashboard"""
        if self._running:
            return
        
        self._running = True
        self._dashboard_task = asyncio.create_task(self._dashboard_loop())
        logger.info("✅ Performance Dashboard started")
    
    async def stop(self):
        """Stop the performance dashboard"""
        self._running = False
        if self._dashboard_task:
            self._dashboard_task.cancel()
            try:
                await self._dashboard_task
            except asyncio.CancelledError:
                pass
        logger.info("🛑 Performance Dashboard stopped")
    
    async def _dashboard_loop(self):
        """Main dashboard monitoring loop"""
        while self._running:
            try:
                # Collect performance data
                summary = await self._collect_performance_data()
                
                # Add to history
                self.performance_history.append(summary)
                
                # Clean old history
                self._clean_old_history()
                
                # Check for alerts
                alerts = self._check_alerts(summary)
                if alerts:
                    await self._handle_alerts(alerts)
                
                # Log performance summary (reduced frequency)
                if len(self.performance_history) % 10 == 0:  # Every 5 minutes
                    await self._log_performance_summary(summary)
                
                await asyncio.sleep(self.update_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in performance dashboard loop: {e}")
                await asyncio.sleep(self.update_interval)
    
    async def _collect_performance_data(self) -> PerformanceSummary:
        """Collect current performance data"""
        try:
            current_time = datetime.now(timezone.utc)
            
            # Callback query statistics
            callback_stats = {}
            if self.callback_manager:
                callback_stats = self.callback_manager.get_stats()
            
            # Performance monitor statistics
            perf_stats = {}
            if self.performance_monitor:
                try:
                    perf_summary = await self.performance_monitor.get_performance_summary()
                    perf_stats = perf_summary.get('current_metrics', {})
                except Exception as e:
                    logger.debug(f"Failed to get performance stats: {e}")
            
            # Concurrent operations statistics
            concurrent_stats = {}
            if self.concurrent_ops:
                concurrent_stats = self.concurrent_ops.get_stats()
            
            # Calculate metrics
            total_queries = callback_stats.get('total_queries', 0)
            successful_queries = callback_stats.get('successful_queries', 0)
            timeout_queries = callback_stats.get('timeout_queries', 0)
            rate_limited_queries = callback_stats.get('rate_limited_queries', 0)
            avg_response_time = callback_stats.get('average_response_time', 0.0) * 1000  # Convert to ms
            
            concurrent_users = perf_stats.get('active_users', 0)
            
            # Calculate system health score
            health_score = self._calculate_health_score(callback_stats, perf_stats, concurrent_stats)
            
            # Generate alerts
            alerts = self._generate_alerts(callback_stats, perf_stats, concurrent_stats)
            
            return PerformanceSummary(
                timestamp=current_time,
                callback_queries_total=total_queries,
                callback_queries_successful=successful_queries,
                callback_queries_timeout=timeout_queries,
                callback_queries_rate_limited=rate_limited_queries,
                average_response_time_ms=avg_response_time,
                concurrent_users=concurrent_users,
                system_health_score=health_score,
                alerts=alerts
            )
            
        except Exception as e:
            logger.error(f"Error collecting performance data: {e}")
            return PerformanceSummary(
                timestamp=datetime.now(timezone.utc),
                callback_queries_total=0,
                callback_queries_successful=0,
                callback_queries_timeout=0,
                callback_queries_rate_limited=0,
                average_response_time_ms=0.0,
                concurrent_users=0,
                system_health_score=0,
                alerts=[f"Data collection error: {str(e)}"]
            )
    
    def _calculate_health_score(self, callback_stats: Dict, perf_stats: Dict, concurrent_stats: Dict) -> int:
        """Calculate overall system health score (0-100)"""
        try:
            score = 100
            
            # Callback query health (40% weight)
            total_queries = callback_stats.get('total_queries', 1)
            if total_queries > 0:
                timeout_rate = callback_stats.get('timeout_queries', 0) / total_queries * 100
                error_rate = callback_stats.get('error_queries', 0) / total_queries * 100
                
                # Deduct points for high timeout/error rates
                score -= min(timeout_rate * 2, 30)  # Max 30 points deduction
                score -= min(error_rate * 3, 20)    # Max 20 points deduction
            
            # Response time health (30% weight) - Adjusted for MongoDB Atlas
            avg_response_time = callback_stats.get('average_response_time', 0.0) * 1000
            if avg_response_time > 5000:  # > 5 seconds (more realistic threshold)
                score -= min((avg_response_time - 5000) / 500, 20)  # Max 20 points deduction
            
            # Concurrent operations health (20% weight)
            success_rate = concurrent_stats.get('success_rate', 100)
            score -= (100 - success_rate) * 0.2  # Max 20 points deduction
            
            # System resource health (10% weight)
            memory_usage = perf_stats.get('memory_usage_mb', 0)
            if memory_usage > 1000:  # > 1GB
                score -= min((memory_usage - 1000) / 100, 10)  # Max 10 points deduction
            
            return max(0, min(100, int(score)))
            
        except Exception as e:
            logger.error(f"Error calculating health score: {e}")
            return 50  # Default moderate health score
    
    def _generate_alerts(self, callback_stats: Dict, perf_stats: Dict, concurrent_stats: Dict) -> List[str]:
        """Generate performance alerts"""
        alerts = []
        
        try:
            # Callback timeout rate alert
            total_queries = callback_stats.get('total_queries', 1)
            if total_queries > 10:  # Only alert if we have enough data
                timeout_rate = callback_stats.get('timeout_queries', 0) / total_queries * 100
                if timeout_rate > self.alert_thresholds['callback_timeout_rate']:
                    alerts.append(f"High callback timeout rate: {timeout_rate:.1f}%")
            
            # Response time alert
            avg_response_time = callback_stats.get('average_response_time', 0.0) * 1000
            if avg_response_time > self.alert_thresholds['average_response_time']:
                alerts.append(f"Slow response time: {avg_response_time:.0f}ms")
            
            # Concurrent users alert
            concurrent_users = perf_stats.get('active_users', 0)
            if concurrent_users > self.alert_thresholds['concurrent_users']:
                alerts.append(f"High concurrent users: {concurrent_users}")
            
            # Memory usage alert
            memory_usage = perf_stats.get('memory_usage_mb', 0)
            if memory_usage > 1000:
                alerts.append(f"High memory usage: {memory_usage}MB")
            
            # Error rate alert
            if total_queries > 10:
                error_rate = callback_stats.get('error_queries', 0) / total_queries * 100
                if error_rate > 5:  # 5% error rate
                    alerts.append(f"High error rate: {error_rate:.1f}%")
            
        except Exception as e:
            alerts.append(f"Alert generation error: {str(e)}")
        
        return alerts
    
    def _check_alerts(self, summary: PerformanceSummary) -> List[str]:
        """Check for performance alerts"""
        return summary.alerts
    
    async def _handle_alerts(self, alerts: List[str]):
        """Handle performance alerts"""
        for alert in alerts:
            logger.warning(f"🚨 PERFORMANCE ALERT: {alert}")
    
    async def _log_performance_summary(self, summary: PerformanceSummary):
        """Log performance summary"""
        logger.info(
            f"📊 Performance Summary: "
            f"Health={summary.system_health_score}/100, "
            f"Queries={summary.callback_queries_successful}/{summary.callback_queries_total}, "
            f"Timeouts={summary.callback_queries_timeout}, "
            f"AvgTime={summary.average_response_time_ms:.0f}ms, "
            f"Users={summary.concurrent_users}"
        )
        
        if summary.alerts:
            logger.warning(f"🚨 Active Alerts: {', '.join(summary.alerts)}")
    
    def _clean_old_history(self):
        """Remove old performance history"""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=self.history_retention_hours)
        self.performance_history = [
            summary for summary in self.performance_history 
            if summary.timestamp > cutoff_time
        ]
    
    def get_current_status(self) -> Dict[str, Any]:
        """Get current performance status"""
        if not self.performance_history:
            return {'status': 'no_data'}
        
        latest = self.performance_history[-1]
        return {
            'status': 'healthy' if latest.system_health_score > 70 else 'degraded',
            'health_score': latest.system_health_score,
            'callback_success_rate': (
                latest.callback_queries_successful / max(latest.callback_queries_total, 1) * 100
            ),
            'average_response_time_ms': latest.average_response_time_ms,
            'concurrent_users': latest.concurrent_users,
            'active_alerts': latest.alerts,
            'last_updated': latest.timestamp.isoformat()
        }
    
    def get_performance_trends(self, hours: int = 1) -> Dict[str, Any]:
        """Get performance trends over specified hours"""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        recent_data = [
            summary for summary in self.performance_history 
            if summary.timestamp > cutoff_time
        ]
        
        if not recent_data:
            return {'status': 'no_data'}
        
        # Calculate trends
        health_scores = [s.system_health_score for s in recent_data]
        response_times = [s.average_response_time_ms for s in recent_data]
        timeout_rates = [
            s.callback_queries_timeout / max(s.callback_queries_total, 1) * 100 
            for s in recent_data
        ]
        
        return {
            'period_hours': hours,
            'data_points': len(recent_data),
            'health_score': {
                'current': health_scores[-1] if health_scores else 0,
                'average': sum(health_scores) / len(health_scores) if health_scores else 0,
                'min': min(health_scores) if health_scores else 0,
                'max': max(health_scores) if health_scores else 0
            },
            'response_time_ms': {
                'current': response_times[-1] if response_times else 0,
                'average': sum(response_times) / len(response_times) if response_times else 0,
                'min': min(response_times) if response_times else 0,
                'max': max(response_times) if response_times else 0
            },
            'timeout_rate_percent': {
                'current': timeout_rates[-1] if timeout_rates else 0,
                'average': sum(timeout_rates) / len(timeout_rates) if timeout_rates else 0,
                'min': min(timeout_rates) if timeout_rates else 0,
                'max': max(timeout_rates) if timeout_rates else 0
            }
        }

# Global performance dashboard instance
performance_dashboard = None

def get_performance_dashboard(callback_manager=None, performance_monitor=None, concurrent_ops=None):
    """Get global performance dashboard instance"""
    global performance_dashboard
    if performance_dashboard is None:
        performance_dashboard = PerformanceDashboard(callback_manager, performance_monitor, concurrent_ops)
    return performance_dashboard
