"""
Smart Logger for Telegram Bot
Reduces console spam from repetitive errors while maintaining important logging
"""

import logging
import time
from typing import Dict, Set
from collections import defaultdict, deque
from datetime import datetime, timezone

class SmartLogger:
    """Smart logger that prevents spam from repetitive errors"""
    
    def __init__(self, logger_name: str = __name__):
        self.logger = logging.getLogger(logger_name)
        
        # Error tracking for spam prevention
        self.error_counts: Dict[str, int] = defaultdict(int)
        self.error_timestamps: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10))
        self.suppressed_errors: Set[str] = set()
        
        # Spam prevention settings
        self.max_same_error_per_minute = 5
        self.max_same_error_per_hour = 20
        self.spam_window_seconds = 60
        self.reset_interval_seconds = 3600  # 1 hour
        
        # Last reset time
        self.last_reset = time.time()
        
        # Callback query specific error patterns
        self.callback_timeout_patterns = [
            "query is too old",
            "query id is invalid",
            "response timeout expired",
            "callback query timeout",
            "query too old"
        ]
        
        # Performance error patterns
        self.performance_patterns = [
            "high memory usage",
            "high cpu usage",
            "slow response time",
            "rate limit exceeded"
        ]
    
    def _should_suppress_error(self, error_msg: str) -> bool:
        """Determine if error should be suppressed to prevent spam"""
        current_time = time.time()
        
        # Reset counters periodically
        if current_time - self.last_reset > self.reset_interval_seconds:
            self._reset_counters()
        
        # Create error signature
        error_signature = self._get_error_signature(error_msg)
        
        # Track error occurrence
        self.error_counts[error_signature] += 1
        self.error_timestamps[error_signature].append(current_time)
        
        # Check if this error type should be suppressed
        recent_count = sum(1 for ts in self.error_timestamps[error_signature] 
                          if current_time - ts < self.spam_window_seconds)
        
        total_count = self.error_counts[error_signature]
        
        # Suppress if too many of the same error recently
        if recent_count > self.max_same_error_per_minute:
            self.suppressed_errors.add(error_signature)
            return True
        
        # Suppress if too many of the same error overall
        if total_count > self.max_same_error_per_hour:
            self.suppressed_errors.add(error_signature)
            return True
        
        return False
    
    def _get_error_signature(self, error_msg: str) -> str:
        """Create a signature for error deduplication"""
        error_lower = error_msg.lower()
        
        # Check for callback timeout errors
        for pattern in self.callback_timeout_patterns:
            if pattern in error_lower:
                return f"callback_timeout:{pattern}"
        
        # Check for performance errors
        for pattern in self.performance_patterns:
            if pattern in error_lower:
                return f"performance:{pattern}"
        
        # For other errors, use first 100 characters
        return error_msg[:100]
    
    def _reset_counters(self):
        """Reset error counters periodically"""
        self.error_counts.clear()
        self.error_timestamps.clear()
        self.suppressed_errors.clear()
        self.last_reset = time.time()
        self.logger.debug("Smart logger counters reset")
    
    def debug(self, msg: str, *args, **kwargs):
        """Log debug message"""
        self.logger.debug(msg, *args, **kwargs)
    
    def info(self, msg: str, *args, **kwargs):
        """Log info message"""
        self.logger.info(msg, *args, **kwargs)
    
    def warning(self, msg: str, *args, **kwargs):
        """Log warning message with spam prevention"""
        if not self._should_suppress_error(msg):
            self.logger.warning(msg, *args, **kwargs)
        else:
            # Log as debug instead
            self.logger.debug(f"[SUPPRESSED WARNING] {msg}", *args, **kwargs)
    
    def error(self, msg: str, *args, **kwargs):
        """Log error message with smart spam prevention"""
        error_signature = self._get_error_signature(msg)
        
        # Always log the first occurrence
        if self.error_counts[error_signature] == 0:
            self.logger.error(msg, *args, **kwargs)
            self.error_counts[error_signature] = 1
            return
        
        # Check if should suppress
        if self._should_suppress_error(msg):
            # Log as debug with suppression notice
            self.logger.debug(f"[SUPPRESSED ERROR] {msg}", *args, **kwargs)
            
            # Periodically log suppression summary
            if self.error_counts[error_signature] % 50 == 0:
                self.logger.warning(
                    f"Error suppressed {self.error_counts[error_signature]} times: {error_signature}"
                )
        else:
            self.logger.error(msg, *args, **kwargs)
    
    def critical(self, msg: str, *args, **kwargs):
        """Log critical message (never suppressed)"""
        self.logger.critical(msg, *args, **kwargs)
    
    def log_callback_timeout(self, user_id: int, data: str):
        """Specialized logging for callback timeouts"""
        msg = f"Callback query timeout for user {user_id}: {data}"
        
        # Use debug level for callback timeouts to reduce spam
        self.debug(msg)
        
        # Track timeout statistics
        timeout_signature = "callback_timeout:general"
        self.error_counts[timeout_signature] += 1
        
        # Log summary every 100 timeouts
        if self.error_counts[timeout_signature] % 100 == 0:
            self.warning(f"Callback timeout summary: {self.error_counts[timeout_signature]} timeouts recorded")
    
    def log_performance_issue(self, issue_type: str, details: str):
        """Specialized logging for performance issues"""
        msg = f"Performance issue - {issue_type}: {details}"
        
        # Use smart suppression for performance issues
        if not self._should_suppress_error(msg):
            self.warning(msg)
        else:
            self.debug(f"[SUPPRESSED PERFORMANCE] {msg}")
    
    def log_rate_limit(self, user_id: int, limit_type: str):
        """Specialized logging for rate limiting"""
        msg = f"Rate limit exceeded for user {user_id}: {limit_type}"
        
        # Use debug level for rate limits to reduce spam
        self.debug(msg)
        
        # Track rate limit statistics
        rate_limit_signature = f"rate_limit:{limit_type}"
        self.error_counts[rate_limit_signature] += 1
        
        # Log summary every 50 rate limits
        if self.error_counts[rate_limit_signature] % 50 == 0:
            self.info(f"Rate limit summary - {limit_type}: {self.error_counts[rate_limit_signature]} occurrences")
    
    def get_suppression_stats(self) -> Dict[str, int]:
        """Get statistics about suppressed errors"""
        return {
            'total_errors': sum(self.error_counts.values()),
            'suppressed_error_types': len(self.suppressed_errors),
            'error_types': len(self.error_counts),
            'suppressed_errors': dict(self.error_counts)
        }
    
    def log_suppression_summary(self):
        """Log a summary of suppressed errors"""
        stats = self.get_suppression_stats()
        
        if stats['suppressed_error_types'] > 0:
            self.info(f"Error suppression summary: {stats['suppressed_error_types']} types suppressed, "
                     f"{stats['total_errors']} total errors tracked")
            
            # Log top suppressed errors
            sorted_errors = sorted(self.error_counts.items(), key=lambda x: x[1], reverse=True)
            for error_sig, count in sorted_errors[:5]:
                if error_sig in self.suppressed_errors:
                    self.info(f"  - {error_sig}: {count} occurrences")

# Global smart logger instances
_smart_loggers: Dict[str, SmartLogger] = {}

def get_smart_logger(name: str = __name__) -> SmartLogger:
    """Get or create a smart logger instance"""
    if name not in _smart_loggers:
        _smart_loggers[name] = SmartLogger(name)
    return _smart_loggers[name]

# Convenience function for the main bot logger
def get_bot_logger() -> SmartLogger:
    """Get the main bot smart logger"""
    return get_smart_logger('telegram_bot')
