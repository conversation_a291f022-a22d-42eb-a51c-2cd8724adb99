"""
Concurrent Operations Manager for High-Scale Telegram Bot
Handles multiple simultaneous user operations without blocking or performance degradation
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timezone
from dataclasses import dataclass
from enum import Enum
import time

from config import Config

logger = logging.getLogger(__name__)

class OperationType(Enum):
    """Types of concurrent operations"""
    DAILY_BONUS = "daily_bonus"
    REFERRAL_PROCESSING = "referral_processing"
    CHANNEL_VERIFICATION = "channel_verification"
    PROFILE_ACCESS = "profile_access"
    BALANCE_UPDATE = "balance_update"
    TRANSACTION_CREATE = "transaction_create"
    USER_UPDATE = "user_update"

@dataclass
class ConcurrentOperation:
    """Represents a concurrent operation"""
    operation_id: str
    user_id: int
    operation_type: OperationType
    function: Callable
    args: tuple
    kwargs: dict
    priority: int = 1
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now(timezone.utc)

class ConcurrentOperationsManager:
    """Manages concurrent user operations for high-scale performance"""
    
    def __init__(self):
        # Operation queues by type for better management
        self.operation_queues: Dict[OperationType, asyncio.Queue] = {}
        self.active_operations: Dict[str, asyncio.Task] = {}
        self.user_operation_locks: Dict[int, asyncio.Lock] = {}
        
        # Performance settings
        self.max_concurrent_ops = getattr(Config, 'MAX_CONCURRENT_TASKS', 200)
        self.max_concurrent_db_ops = getattr(Config, 'MAX_CONCURRENT_DB_OPS', 100)
        self.operation_timeout = getattr(Config, 'TASK_TIMEOUT', 30)
        
        # Semaphores for controlling concurrency
        self.global_semaphore = asyncio.Semaphore(self.max_concurrent_ops)
        self.db_semaphore = asyncio.Semaphore(self.max_concurrent_db_ops)
        
        # Statistics
        self.stats = {
            'total_operations': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'timeout_operations': 0,
            'concurrent_operations': 0,
            'operations_by_type': {op_type: 0 for op_type in OperationType}
        }
        
        # Initialize queues
        for op_type in OperationType:
            self.operation_queues[op_type] = asyncio.Queue(maxsize=1000)
        
        self._workers_started = False
        self._worker_tasks: List[asyncio.Task] = []
    
    async def start_workers(self):
        """Start worker tasks for processing operations"""
        if self._workers_started:
            return
        
        self._workers_started = True
        worker_count = getattr(Config, 'WORKER_THREADS', 20)
        
        # Start workers for each operation type
        for op_type in OperationType:
            for i in range(max(2, worker_count // len(OperationType))):
                worker_task = asyncio.create_task(
                    self._operation_worker(f"{op_type.value}_worker_{i}", op_type)
                )
                self._worker_tasks.append(worker_task)
        
        logger.info(f"Started {len(self._worker_tasks)} concurrent operation workers")
    
    async def stop_workers(self):
        """Stop all worker tasks"""
        self._workers_started = False
        
        for task in self._worker_tasks:
            task.cancel()
        
        await asyncio.gather(*self._worker_tasks, return_exceptions=True)
        self._worker_tasks.clear()
        
        logger.info("Stopped all concurrent operation workers")
    
    async def execute_operation(self, user_id: int, operation_type: OperationType, 
                              function: Callable, *args, priority: int = 1, **kwargs) -> Any:
        """Execute operation with concurrency control"""
        operation_id = f"{operation_type.value}_{user_id}_{int(time.time() * 1000)}"
        
        operation = ConcurrentOperation(
            operation_id=operation_id,
            user_id=user_id,
            operation_type=operation_type,
            function=function,
            args=args,
            kwargs=kwargs,
            priority=priority
        )
        
        # Add to appropriate queue
        try:
            await self.operation_queues[operation_type].put(operation)
            self.stats['total_operations'] += 1
            self.stats['operations_by_type'][operation_type] += 1
            
            logger.debug(f"Queued operation {operation_id} for user {user_id}")
            return operation_id
            
        except asyncio.QueueFull:
            logger.warning(f"Operation queue full for {operation_type.value}")
            raise Exception(f"System busy, please try again later")
    
    async def execute_daily_bonus_operation(self, user_id: int, function: Callable, *args, **kwargs) -> Any:
        """Execute daily bonus operation with user-level locking"""
        async with self._get_user_lock(user_id):
            async with self.global_semaphore:
                async with self.db_semaphore:
                    try:
                        result = await asyncio.wait_for(
                            function(*args, **kwargs),
                            timeout=self.operation_timeout
                        )
                        self.stats['successful_operations'] += 1
                        return result
                    except asyncio.TimeoutError:
                        self.stats['timeout_operations'] += 1
                        logger.warning(f"Daily bonus operation timeout for user {user_id}")
                        raise
                    except Exception as e:
                        self.stats['failed_operations'] += 1
                        logger.error(f"Daily bonus operation failed for user {user_id}: {e}")
                        raise
    
    async def execute_referral_operation(self, user_id: int, function: Callable, *args, **kwargs) -> Any:
        """Execute referral processing operation"""
        async with self.global_semaphore:
            async with self.db_semaphore:
                try:
                    result = await asyncio.wait_for(
                        function(*args, **kwargs),
                        timeout=self.operation_timeout
                    )
                    self.stats['successful_operations'] += 1
                    return result
                except asyncio.TimeoutError:
                    self.stats['timeout_operations'] += 1
                    logger.warning(f"Referral operation timeout for user {user_id}")
                    raise
                except Exception as e:
                    self.stats['failed_operations'] += 1
                    logger.error(f"Referral operation failed for user {user_id}: {e}")
                    raise
    
    async def execute_channel_verification(self, user_id: int, function: Callable, *args, **kwargs) -> Any:
        """Execute channel verification with caching optimization"""
        # Channel verification can be done with lighter concurrency control
        async with self.global_semaphore:
            try:
                result = await asyncio.wait_for(
                    function(*args, **kwargs),
                    timeout=15  # Shorter timeout for channel verification
                )
                self.stats['successful_operations'] += 1
                return result
            except asyncio.TimeoutError:
                self.stats['timeout_operations'] += 1
                logger.warning(f"Channel verification timeout for user {user_id}")
                raise
            except Exception as e:
                self.stats['failed_operations'] += 1
                logger.error(f"Channel verification failed for user {user_id}: {e}")
                raise
    
    async def execute_profile_access(self, user_id: int, function: Callable, *args, **kwargs) -> Any:
        """Execute profile access operation (read-heavy, optimized for concurrency)"""
        # Profile access is read-heavy, so we can allow more concurrency
        try:
            result = await asyncio.wait_for(
                function(*args, **kwargs),
                timeout=10  # Quick timeout for profile access
            )
            self.stats['successful_operations'] += 1
            return result
        except asyncio.TimeoutError:
            self.stats['timeout_operations'] += 1
            logger.warning(f"Profile access timeout for user {user_id}")
            raise
        except Exception as e:
            self.stats['failed_operations'] += 1
            logger.error(f"Profile access failed for user {user_id}: {e}")
            raise
    
    async def batch_execute_operations(self, operations: List[Dict[str, Any]]) -> List[Any]:
        """Execute multiple operations in batch with optimal concurrency"""
        # Group operations by type for better performance
        grouped_operations = {}
        for op in operations:
            op_type = op.get('type', OperationType.PROFILE_ACCESS)
            if op_type not in grouped_operations:
                grouped_operations[op_type] = []
            grouped_operations[op_type].append(op)
        
        results = []
        
        # Execute each group with appropriate concurrency limits
        for op_type, ops in grouped_operations.items():
            if op_type in [OperationType.DAILY_BONUS, OperationType.BALANCE_UPDATE]:
                # Serialize operations that modify user balance
                for op in ops:
                    result = await self._execute_single_operation(op)
                    results.append(result)
            else:
                # Execute read operations concurrently
                batch_size = 50  # Process in batches of 50
                for i in range(0, len(ops), batch_size):
                    batch = ops[i:i + batch_size]
                    batch_results = await asyncio.gather(
                        *[self._execute_single_operation(op) for op in batch],
                        return_exceptions=True
                    )
                    results.extend(batch_results)
        
        return results
    
    async def _operation_worker(self, worker_name: str, operation_type: OperationType):
        """Worker task for processing operations of a specific type"""
        logger.debug(f"Worker {worker_name} started for {operation_type.value}")
        
        while self._workers_started:
            try:
                # Get operation from queue
                operation = await asyncio.wait_for(
                    self.operation_queues[operation_type].get(),
                    timeout=1.0
                )
                
                # Execute operation based on type
                self.stats['concurrent_operations'] += 1
                
                try:
                    if operation_type == OperationType.DAILY_BONUS:
                        result = await self.execute_daily_bonus_operation(
                            operation.user_id, operation.function, *operation.args, **operation.kwargs
                        )
                    elif operation_type == OperationType.REFERRAL_PROCESSING:
                        result = await self.execute_referral_operation(
                            operation.user_id, operation.function, *operation.args, **operation.kwargs
                        )
                    elif operation_type == OperationType.CHANNEL_VERIFICATION:
                        result = await self.execute_channel_verification(
                            operation.user_id, operation.function, *operation.args, **operation.kwargs
                        )
                    elif operation_type == OperationType.PROFILE_ACCESS:
                        result = await self.execute_profile_access(
                            operation.user_id, operation.function, *operation.args, **operation.kwargs
                        )
                    else:
                        # Default execution
                        result = await operation.function(*operation.args, **operation.kwargs)
                    
                    logger.debug(f"Operation {operation.operation_id} completed successfully")
                    
                except Exception as e:
                    logger.error(f"Operation {operation.operation_id} failed: {e}")
                
                finally:
                    self.stats['concurrent_operations'] -= 1
                    self.operation_queues[operation_type].task_done()
                
            except asyncio.TimeoutError:
                # No operations in queue, continue
                continue
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Worker {worker_name} error: {e}")
        
        logger.debug(f"Worker {worker_name} stopped")
    
    async def _execute_single_operation(self, operation_data: Dict[str, Any]) -> Any:
        """Execute a single operation"""
        try:
            function = operation_data['function']
            args = operation_data.get('args', ())
            kwargs = operation_data.get('kwargs', {})
            
            return await function(*args, **kwargs)
        except Exception as e:
            logger.error(f"Single operation failed: {e}")
            return None
    
    def _get_user_lock(self, user_id: int) -> asyncio.Lock:
        """Get or create a lock for a specific user"""
        if user_id not in self.user_operation_locks:
            self.user_operation_locks[user_id] = asyncio.Lock()
        return self.user_operation_locks[user_id]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get operation statistics"""
        total_ops = self.stats['total_operations']
        success_rate = (self.stats['successful_operations'] / total_ops * 100) if total_ops > 0 else 0
        
        return {
            **self.stats,
            'success_rate': round(success_rate, 2),
            'queue_sizes': {
                op_type.value: self.operation_queues[op_type].qsize() 
                for op_type in OperationType
            },
            'active_user_locks': len(self.user_operation_locks),
            'workers_active': len(self._worker_tasks)
        }

# Global concurrent operations manager
concurrent_ops_manager = None

def get_concurrent_operations_manager() -> ConcurrentOperationsManager:
    """Get global concurrent operations manager instance"""
    global concurrent_ops_manager
    if concurrent_ops_manager is None:
        concurrent_ops_manager = ConcurrentOperationsManager()
    return concurrent_ops_manager
