import os
from dotenv import load_dotenv
from typing import List, Optional
import logging

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class Config:
    """Configuration class for the Telegram Referral Bot"""
    
    # Bot Configuration
    BOT_TOKEN = os.getenv('BOT_TOKEN')
    BOT_USERNAME = os.getenv('BOT_USERNAME')
    
    # Database Configuration
    MONGODB_URI = os.getenv('MONGODB_URI')
    DATABASE_NAME = os.getenv('DATABASE_NAME', 'referral_bot_db')
    
    # Admin Configuration
    ADMIN_USER_IDS = [int(id.strip()) for id in os.getenv('ADMIN_USER_IDS', '').split(',') if id.strip()]
    ADMIN_PASSWORD = os.getenv('ADMIN_PASSWORD', 'admin123')
    PURCHASE_LOG_CHANNEL_ID = int(os.getenv('PURCHASE_LOG_CHANNEL_ID', 0)) if os.getenv('PURCHASE_LOG_CHANNEL_ID') else None
    TASK_REVIEW_ADMIN_ID = int(os.getenv('TASK_REVIEW_ADMIN_ID', 0)) if os.getenv('TASK_REVIEW_ADMIN_ID') else None
    
    # Bot Settings
    REFERRAL_REWARD = int(os.getenv('REFERRAL_REWARD', 10))
    DAILY_BONUS_AMOUNT = os.getenv('DAILY_BONUS_AMOUNT', '8')  # Support range format like "8-20"
    MINIMUM_WITHDRAWAL = int(os.getenv('MINIMUM_WITHDRAWAL', 50))
    WITHDRAWAL_COOLDOWN_HOURS = int(os.getenv('WITHDRAWAL_COOLDOWN_HOURS', 24))
    DEFAULT_DAILY_NOTIFICATIONS = os.getenv('DEFAULT_DAILY_NOTIFICATIONS', 'true').lower() == 'true'
    
    # Security
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-change-this')
    RATE_LIMIT_MESSAGES = int(os.getenv('RATE_LIMIT_MESSAGES', 20))
    RATE_LIMIT_WINDOW = int(os.getenv('RATE_LIMIT_WINDOW', 60))
    
    # Logging
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'bot.log')
    
    # Channel Settings - Forced Subscription Configuration
    REQUIRED_CHANNEL_1 = int(os.getenv('REQUIRED_CHANNEL_1', '-1001296547211'))
    REQUIRED_CHANNEL_2 = int(os.getenv('REQUIRED_CHANNEL_2', '-1002414699235'))
    JOIN_LINK_1 = os.getenv('JOIN_LINK_1', 'https://t.me/+ec_CC8b-gUxjNjQ1')
    JOIN_LINK_2 = os.getenv('JOIN_LINK_2', 'https://t.me/+0vJ8rUZLPTE2ZDhl')

    # Legacy channel setting (kept for backward compatibility)
    REQUIRED_CHANNELS = [ch.strip() for ch in os.getenv('REQUIRED_CHANNELS', '').split(',') if ch.strip()]

    # Bot Mode - Always use long polling (no webhook configuration)
    USE_POLLING = True
    
    # Currency
    CURRENCY_SYMBOL = '💎'
    CURRENCY_NAME = 'INR'
    
    # Bot Messages
    WELCOME_MESSAGE = """
🎉 Welcome to the Referral Earning Bot! 🎉

💰 Earn {currency}{referral_reward} for each successful referral!
🎁 Get daily bonuses!
💎 Withdraw at {currency}{min_withdrawal} minimum!

Start earning now by inviting your friends! 🚀
    """.format(
        currency=CURRENCY_SYMBOL,
        referral_reward=REFERRAL_REWARD,
        min_withdrawal=MINIMUM_WITHDRAWAL
    )
    
    # Product Catalog (can be managed through admin panel)
    @classmethod
    def get_default_products(cls):
        """Get default products with dynamic pricing based on minimum withdrawal"""
        base_price = cls.MINIMUM_WITHDRAWAL * 5  # Products are 5x minimum withdrawal
        return [
            {
                'name': 'Canva Pro (1 Month)',
                'price': base_price,
                'description': 'Premium Canva subscription with all features unlocked',
                'category': 'Design Tools'
            },
            {
                'name': 'Spotify Premium (1 Month)',
                'price': base_price,
                'description': 'Ad-free music streaming with offline downloads',
                'category': 'Entertainment'
            },
            {
                'name': 'Netflix Premium (1 Month)',
                'price': base_price,
                'description': 'Premium Netflix subscription with 4K streaming',
                'category': 'Entertainment'
            },
            {
                'name': 'Adobe Creative Cloud (1 Month)',
                'price': base_price,
                'description': 'Access to all Adobe creative applications',
                'category': 'Design Tools'
            }
        ]

    # Backward compatibility - will be deprecated
    DEFAULT_PRODUCTS = []
    
    @classmethod
    def reload_config(cls):
        """Reload configuration from environment variables"""
        try:
            logger.info("🔄 Reloading configuration from environment variables...")

            # Reload .env file
            load_dotenv(override=True)

            # Update channel configuration
            cls.REQUIRED_CHANNEL_1 = int(os.getenv('REQUIRED_CHANNEL_1', '-1001296547211'))
            cls.REQUIRED_CHANNEL_2 = int(os.getenv('REQUIRED_CHANNEL_2', '-1002414699235'))
            cls.JOIN_LINK_1 = os.getenv('JOIN_LINK_1', 'https://t.me/+ec_CC8b-gUxjNjQ1')
            cls.JOIN_LINK_2 = os.getenv('JOIN_LINK_2', 'https://t.me/+0vJ8rUZLPTE2ZDhl')

            # Update other dynamic settings
            cls.REFERRAL_REWARD = int(os.getenv('REFERRAL_REWARD', 10))
            cls.DAILY_BONUS_AMOUNT = os.getenv('DAILY_BONUS_AMOUNT', '8')  # Support range format
            cls.MINIMUM_WITHDRAWAL = int(os.getenv('MINIMUM_WITHDRAWAL', 10))  # Fixed: Use correct default
            cls.WITHDRAWAL_COOLDOWN_HOURS = int(os.getenv('WITHDRAWAL_COOLDOWN_HOURS', 24))

            logger.info(f"✅ Configuration reloaded successfully")
            logger.info(f"📺 Channel Configuration:")
            logger.info(f"   - Channel 1: {cls.REQUIRED_CHANNEL_1}")
            logger.info(f"   - Channel 2: {cls.REQUIRED_CHANNEL_2}")
            logger.info(f"   - Join Link 1: {cls.JOIN_LINK_1}")
            logger.info(f"   - Join Link 2: {cls.JOIN_LINK_2}")
            logger.info(f"💰 Financial Settings:")
            logger.info(f"   - Referral Reward: 💎{cls.REFERRAL_REWARD}")
            logger.info(f"   - Daily Bonus: 💎{cls.DAILY_BONUS_AMOUNT}")
            logger.info(f"   - Minimum Withdrawal: 💎{cls.MINIMUM_WITHDRAWAL}")
            logger.info(f"   - Withdrawal Cooldown: {cls.WITHDRAWAL_COOLDOWN_HOURS}h")

            return True

        except Exception as e:
            logger.error(f"❌ Failed to reload configuration: {e}")
            return False

    @classmethod
    def parse_daily_bonus_amount(cls, bonus_config: str) -> tuple[int, int]:
        """
        Parse daily bonus amount configuration for integer values.

        Args:
            bonus_config: String like "8" (single value) or "8-20" (range)

        Returns:
            tuple: (min_amount, max_amount) - same values for single amount (integers only)

        Raises:
            ValueError: If format is invalid
        """
        try:
            bonus_config = str(bonus_config).strip()

            if '-' in bonus_config:
                # Range format: "8-20"
                parts = bonus_config.split('-')
                if len(parts) != 2:
                    raise ValueError(f"Invalid range format: {bonus_config}. Expected format: 'min-max'")

                # Convert to integers (will round decimal values)
                min_amount = int(round(float(parts[0].strip())))
                max_amount = int(round(float(parts[1].strip())))

                if min_amount < 0 or max_amount < 0:
                    raise ValueError(f"Bonus amounts cannot be negative: {bonus_config}")

                if min_amount > max_amount:
                    raise ValueError(f"Minimum amount cannot be greater than maximum: {bonus_config}")

                return min_amount, max_amount
            else:
                # Single value format: "8"
                amount = int(round(float(bonus_config)))
                if amount < 0:
                    raise ValueError(f"Bonus amount cannot be negative: {bonus_config}")

                return amount, amount

        except ValueError as e:
            raise e
        except Exception as e:
            raise ValueError(f"Invalid daily bonus format: {bonus_config}. Expected single number or range like '8-20'") from e

    @classmethod
    def get_random_daily_bonus(cls) -> int:
        """
        Get a random daily bonus amount based on configuration (integer values only).

        Returns:
            int: Random bonus amount within configured range
        """
        import random

        try:
            min_amount, max_amount = cls.parse_daily_bonus_amount(cls.DAILY_BONUS_AMOUNT)

            if min_amount == max_amount:
                return min_amount

            # Generate random integer within range (inclusive)
            return random.randint(min_amount, max_amount)

        except Exception as e:
            logger.error(f"Failed to generate random daily bonus: {e}. Using default value 8")
            return 8

    @classmethod
    def get_required_channels(cls):
        """Get list of required channel IDs"""
        return [cls.REQUIRED_CHANNEL_1, cls.REQUIRED_CHANNEL_2]

    @classmethod
    def get_join_links(cls):
        """Get list of join links"""
        return [cls.JOIN_LINK_1, cls.JOIN_LINK_2]

    @classmethod
    def log_configuration(cls):
        """Log current configuration values for debugging (to file only)"""
        logger.debug("🔧 Current Configuration Values:")
        logger.debug("=" * 50)
        logger.debug(f"🤖 Bot Settings:")
        logger.debug(f"   - Bot Token: {'✅ Set' if cls.BOT_TOKEN else '❌ Missing'}")
        logger.debug(f"   - Bot Username: {cls.BOT_USERNAME or 'Not set'}")
        logger.debug(f"🗄️ Database:")
        logger.debug(f"   - MongoDB URI: {'✅ Set' if cls.MONGODB_URI else '❌ Missing'}")
        logger.debug(f"   - Database Name: {cls.DATABASE_NAME}")
        logger.debug(f"💰 Financial Settings:")
        logger.debug(f"   - Referral Reward: 💎{cls.REFERRAL_REWARD}")
        logger.debug(f"   - Daily Bonus: 💎{cls.DAILY_BONUS_AMOUNT}")
        logger.debug(f"   - Minimum Withdrawal: 💎{cls.MINIMUM_WITHDRAWAL}")
        logger.debug(f"   - Withdrawal Cooldown: {cls.WITHDRAWAL_COOLDOWN_HOURS}h")
        logger.debug(f"📺 Channels:")
        logger.debug(f"   - Channel 1: {cls.REQUIRED_CHANNEL_1}")
        logger.debug(f"   - Channel 2: {cls.REQUIRED_CHANNEL_2}")
        logger.debug(f"🔗 Join Links:")
        logger.debug(f"   - Link 1: {cls.JOIN_LINK_1}")
        logger.debug(f"   - Link 2: {cls.JOIN_LINK_2}")
        logger.debug("=" * 50)

    @classmethod
    def validate_config(cls):
        """Validate required configuration"""
        required_vars = ['BOT_TOKEN', 'MONGODB_URI']
        missing_vars = []

        for var in required_vars:
            if not getattr(cls, var):
                missing_vars.append(var)

        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

        # Validate channel configuration
        try:
            channels = cls.get_required_channels()
            links = cls.get_join_links()

            if len(channels) != 2:
                logger.warning("⚠️ Expected 2 required channels, got {len(channels)}")

            if len(links) != 2:
                logger.warning("⚠️ Expected 2 join links, got {len(links)}")

            logger.info(f"✅ Channel configuration validated: {len(channels)} channels, {len(links)} links")

        except Exception as e:
            logger.error(f"❌ Channel configuration validation failed: {e}")

        return True
