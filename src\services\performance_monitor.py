"""
Performance Monitoring Service for High-Scale Telegram Bot
Monitors system performance, database operations, and user activity
"""

import asyncio
import logging
import time
import psutil
from typing import Dict, Any, List
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Performance metrics data structure"""
    timestamp: datetime
    active_users: int
    concurrent_operations: int
    response_time_avg: float
    memory_usage_mb: float
    cpu_usage_percent: float
    database_connections: int
    cache_hit_rate: float
    error_rate: float
    requests_per_minute: int

class PerformanceMonitor:
    """Real-time performance monitoring for scalable bot operations"""
    
    def __init__(self, database, cache_service, async_optimizer):
        self.database = database
        self.cache_service = cache_service
        self.async_optimizer = async_optimizer
        
        # Metrics storage
        self.metrics_history: List[PerformanceMetrics] = []
        self.max_history_size = 1440  # 24 hours of minute-by-minute data
        
        # Performance counters
        self.request_counter = 0
        self.error_counter = 0
        self.response_times: List[float] = []
        self.active_users_set = set()
        
        # Monitoring settings
        self.monitoring_interval = 60  # 1 minute
        self.alert_thresholds = {
            'response_time_ms': 2000,  # 2 seconds
            'memory_usage_mb': 1000,   # 1GB
            'cpu_usage_percent': 80,   # 80%
            'error_rate_percent': 1,   # 1%
            'cache_hit_rate_percent': 70,  # 70%
            'callback_timeout_rate_percent': 10,  # 10% of callback queries
            'callback_response_time_ms': 1000  # 1 second for callbacks
        }
        
        self._monitoring_task = None
        self._running = False
    
    async def start_monitoring(self):
        """Start performance monitoring"""
        if self._running:
            return
        
        self._running = True
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Performance monitoring started")
    
    async def stop_monitoring(self):
        """Stop performance monitoring"""
        self._running = False
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("Performance monitoring stopped")
    
    async def record_request(self, user_id: int, response_time: float, success: bool = True):
        """Record a request for performance tracking"""
        self.request_counter += 1
        self.response_times.append(response_time)
        self.active_users_set.add(user_id)
        
        if not success:
            self.error_counter += 1
        
        # Keep response times list manageable
        if len(self.response_times) > 1000:
            self.response_times = self.response_times[-500:]

    async def record_callback_query(self, user_id: int, response_time: float, success: bool = True,
                                  timeout: bool = False):
        """Record callback query performance metrics"""
        try:
            # Record as regular request
            await self.record_request(user_id, response_time, success)

            # Additional callback-specific tracking could be added here
            if timeout:
                logger.debug(f"Callback query timeout recorded for user {user_id}")

        except Exception as e:
            logger.error(f"Error recording callback query: {e}")

    async def get_current_metrics(self) -> PerformanceMetrics:
        """Get current performance metrics"""
        try:
            # Calculate response time average
            avg_response_time = sum(self.response_times) / len(self.response_times) if self.response_times else 0
            
            # Get system metrics
            memory_usage = psutil.virtual_memory().used / (1024 * 1024)  # MB
            cpu_usage = psutil.cpu_percent(interval=0.1)
            
            # Get database connection count
            db_connections = await self._get_database_connections()
            
            # Get cache statistics
            cache_stats = await self.cache_service.get_cache_stats()
            cache_hit_rate = cache_stats.get('hit_rate', 0)
            
            # Calculate error rate
            total_requests = self.request_counter
            error_rate = (self.error_counter / total_requests * 100) if total_requests > 0 else 0
            
            # Get async optimizer stats
            optimizer_stats = self.async_optimizer.get_stats()
            concurrent_operations = optimizer_stats.get('concurrent_tasks', 0)
            
            metrics = PerformanceMetrics(
                timestamp=datetime.now(timezone.utc),
                active_users=len(self.active_users_set),
                concurrent_operations=concurrent_operations,
                response_time_avg=avg_response_time,
                memory_usage_mb=memory_usage,
                cpu_usage_percent=cpu_usage,
                database_connections=db_connections,
                cache_hit_rate=cache_hit_rate,
                error_rate=error_rate,
                requests_per_minute=self.request_counter
            )
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to get current metrics: {e}")
            return PerformanceMetrics(
                timestamp=datetime.now(timezone.utc),
                active_users=0, concurrent_operations=0, response_time_avg=0,
                memory_usage_mb=0, cpu_usage_percent=0, database_connections=0,
                cache_hit_rate=0, error_rate=0, requests_per_minute=0
            )
    
    async def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        current_metrics = await self.get_current_metrics()
        
        # Calculate trends from history
        recent_metrics = self.metrics_history[-60:] if len(self.metrics_history) >= 60 else self.metrics_history
        
        trends = {}
        if recent_metrics:
            trends = {
                'response_time_trend': self._calculate_trend([m.response_time_avg for m in recent_metrics]),
                'memory_usage_trend': self._calculate_trend([m.memory_usage_mb for m in recent_metrics]),
                'active_users_trend': self._calculate_trend([m.active_users for m in recent_metrics]),
                'error_rate_trend': self._calculate_trend([m.error_rate for m in recent_metrics])
            }
        
        # Check for alerts
        alerts = self._check_alerts(current_metrics)
        
        summary = {
            'current_metrics': asdict(current_metrics),
            'trends': trends,
            'alerts': alerts,
            'system_health': self._calculate_health_score(current_metrics),
            'recommendations': self._get_recommendations(current_metrics)
        }
        
        return summary
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self._running:
            try:
                # Collect metrics
                metrics = await self.get_current_metrics()
                
                # Store metrics
                self.metrics_history.append(metrics)
                
                # Trim history if too large
                if len(self.metrics_history) > self.max_history_size:
                    self.metrics_history = self.metrics_history[-self.max_history_size:]
                
                # Check for alerts
                alerts = self._check_alerts(metrics)
                if alerts:
                    for alert in alerts:
                        logger.warning(f"Performance Alert: {alert}")
                
                # Reset counters for next interval
                self.request_counter = 0
                self.error_counter = 0
                self.response_times.clear()
                self.active_users_set.clear()
                
                # Wait for next interval
                await asyncio.sleep(self.monitoring_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(self.monitoring_interval)
    
    async def _get_database_connections(self) -> int:
        """Get current database connection count"""
        try:
            # This is a simplified approach - actual implementation depends on MongoDB driver
            return len(self.database.client.nodes) if self.database.client else 0
        except Exception:
            return 0
    
    def _calculate_trend(self, values: List[float]) -> str:
        """Calculate trend direction from values"""
        if len(values) < 2:
            return "stable"
        
        recent_avg = sum(values[-5:]) / len(values[-5:]) if len(values) >= 5 else values[-1]
        older_avg = sum(values[:5]) / len(values[:5]) if len(values) >= 5 else values[0]
        
        if recent_avg > older_avg * 1.1:
            return "increasing"
        elif recent_avg < older_avg * 0.9:
            return "decreasing"
        else:
            return "stable"
    
    def _check_alerts(self, metrics: PerformanceMetrics) -> List[str]:
        """Check for performance alerts"""
        alerts = []
        
        if metrics.response_time_avg > self.alert_thresholds['response_time_ms']:
            alerts.append(f"High response time: {metrics.response_time_avg:.2f}ms")
        
        if metrics.memory_usage_mb > self.alert_thresholds['memory_usage_mb']:
            alerts.append(f"High memory usage: {metrics.memory_usage_mb:.2f}MB")
        
        if metrics.cpu_usage_percent > self.alert_thresholds['cpu_usage_percent']:
            alerts.append(f"High CPU usage: {metrics.cpu_usage_percent:.2f}%")
        
        if metrics.error_rate > self.alert_thresholds['error_rate_percent']:
            alerts.append(f"High error rate: {metrics.error_rate:.2f}%")
        
        if metrics.cache_hit_rate < self.alert_thresholds['cache_hit_rate_percent']:
            alerts.append(f"Low cache hit rate: {metrics.cache_hit_rate:.2f}%")
        
        return alerts
    
    def _calculate_health_score(self, metrics: PerformanceMetrics) -> int:
        """Calculate overall system health score (0-100)"""
        score = 100
        
        # Deduct points for performance issues
        if metrics.response_time_avg > 1000:
            score -= 20
        elif metrics.response_time_avg > 500:
            score -= 10
        
        if metrics.memory_usage_mb > 800:
            score -= 15
        elif metrics.memory_usage_mb > 500:
            score -= 8
        
        if metrics.cpu_usage_percent > 70:
            score -= 15
        elif metrics.cpu_usage_percent > 50:
            score -= 8
        
        if metrics.error_rate > 0.5:
            score -= 20
        elif metrics.error_rate > 0.1:
            score -= 10
        
        if metrics.cache_hit_rate < 60:
            score -= 10
        elif metrics.cache_hit_rate < 80:
            score -= 5
        
        return max(0, score)
    
    def _get_recommendations(self, metrics: PerformanceMetrics) -> List[str]:
        """Get performance optimization recommendations"""
        recommendations = []
        
        if metrics.response_time_avg > 1000:
            recommendations.append("Consider enabling Redis caching for better response times")
        
        if metrics.memory_usage_mb > 800:
            recommendations.append("High memory usage detected - consider optimizing data structures")
        
        if metrics.cache_hit_rate < 70:
            recommendations.append("Low cache hit rate - review caching strategy")
        
        if metrics.error_rate > 0.5:
            recommendations.append("High error rate detected - review error handling")
        
        if metrics.concurrent_operations > 80:
            recommendations.append("High concurrent operations - consider increasing worker threads")
        
        return recommendations

# Global performance monitor instance
performance_monitor = None

def get_performance_monitor(database=None, cache_service=None, async_optimizer=None):
    """Get global performance monitor instance"""
    global performance_monitor
    if performance_monitor is None and all([database, cache_service, async_optimizer]):
        performance_monitor = PerformanceMonitor(database, cache_service, async_optimizer)
    return performance_monitor
