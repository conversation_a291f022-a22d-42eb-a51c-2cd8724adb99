"""
Enhanced Callback Query Manager for High-Performance Telegram Bot
Handles callback query timeouts, rate limiting, and performance optimization
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, Callable, Set
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from enum import Enum
import hashlib

from telegram import Update, CallbackQuery
from telegram.ext import ContextTypes
from telegram.error import BadRequest, TimedOut, NetworkError

from config import Config

logger = logging.getLogger(__name__)

class CallbackPriority(Enum):
    """Priority levels for callback queries"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class CallbackQueryInfo:
    """Information about a callback query"""
    query_id: str
    user_id: int
    data: str
    timestamp: datetime
    priority: CallbackPriority
    attempts: int = 0
    max_attempts: int = 3

class CallbackQueryManager:
    """Enhanced callback query manager with timeout handling and performance optimization"""
    
    def __init__(self):
        # Query tracking and rate limiting
        self.active_queries: Dict[str, CallbackQueryInfo] = {}
        self.user_query_counts: Dict[int, int] = {}
        self.user_last_query: Dict[int, datetime] = {}
        self.processed_queries: Set[str] = set()
        
        # Performance settings
        self.max_query_age_seconds = getattr(Config, 'CALLBACK_QUERY_MAX_AGE', 30)
        self.max_queries_per_user_per_minute = getattr(Config, 'MAX_QUERIES_PER_USER_PER_MINUTE', 20)
        self.query_timeout_seconds = getattr(Config, 'CALLBACK_QUERY_TIMEOUT', 25)
        self.max_concurrent_queries = getattr(Config, 'MAX_CONCURRENT_CALLBACK_QUERIES', 100)
        
        # Rate limiting
        self.rate_limit_window = 60  # 1 minute
        self.rate_limit_cleanup_interval = 300  # 5 minutes
        
        # Performance monitoring
        self.stats = {
            'total_queries': 0,
            'successful_queries': 0,
            'timeout_queries': 0,
            'rate_limited_queries': 0,
            'duplicate_queries': 0,
            'error_queries': 0,
            'average_response_time': 0.0
        }
        
        # Cleanup task
        self._cleanup_task = None
        self._running = False
    
    async def start(self):
        """Start the callback query manager"""
        if self._running:
            return
        
        self._running = True
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        logger.info("✅ Callback Query Manager started")
    
    async def stop(self):
        """Stop the callback query manager"""
        self._running = False
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        logger.info("🛑 Callback Query Manager stopped")
    
    async def handle_callback_query(self, update: Update, context: ContextTypes.DEFAULT_TYPE, 
                                  handler_func: Callable) -> bool:
        """
        Enhanced callback query handler with timeout prevention and performance optimization
        
        Returns:
            bool: True if query was handled successfully, False if skipped/failed
        """
        try:
            query = update.callback_query
            if not query:
                return False
            
            user_id = query.from_user.id
            query_id = query.id
            data = query.data or ""
            
            # Generate unique query hash for duplicate detection
            query_hash = self._generate_query_hash(query_id, user_id, data)
            
            start_time = time.time()
            self.stats['total_queries'] += 1
            
            # Check if query is too old (prevent timeout errors)
            if self._is_query_too_old(query):
                logger.debug(f"Skipping old callback query from user {user_id}: {data}")
                self.stats['timeout_queries'] += 1
                return False
            
            # Check for duplicate queries
            if query_hash in self.processed_queries:
                logger.debug(f"Skipping duplicate callback query from user {user_id}: {data}")
                self.stats['duplicate_queries'] += 1
                try:
                    await query.answer("⏳ Processing previous request...")
                except Exception:
                    pass
                return False
            
            # Rate limiting check
            if not self._check_rate_limit(user_id):
                logger.warning(f"Rate limit exceeded for user {user_id}")
                self.stats['rate_limited_queries'] += 1
                try:
                    await query.answer("⚠️ Too many requests. Please wait a moment.", show_alert=True)
                except Exception:
                    pass
                return False
            
            # Check concurrent query limit
            if len(self.active_queries) >= self.max_concurrent_queries:
                logger.warning(f"Max concurrent queries reached: {len(self.active_queries)}")
                try:
                    await query.answer("🔄 System busy. Please try again in a moment.", show_alert=True)
                except Exception:
                    pass
                return False
            
            # Create query info
            query_info = CallbackQueryInfo(
                query_id=query_id,
                user_id=user_id,
                data=data,
                timestamp=datetime.now(timezone.utc),
                priority=self._determine_priority(data)
            )
            
            # Track active query
            self.active_queries[query_hash] = query_info
            self.processed_queries.add(query_hash)
            
            try:
                # Answer the query immediately to prevent timeout
                await self._answer_query_safely(query)
                
                # Execute the handler with timeout
                await asyncio.wait_for(
                    handler_func(update, context),
                    timeout=self.query_timeout_seconds
                )
                
                # Update statistics
                response_time = time.time() - start_time
                self._update_response_time_stats(response_time)
                self.stats['successful_queries'] += 1
                
                logger.debug(f"Callback query handled successfully in {response_time:.2f}s: {data}")
                return True
                
            except asyncio.TimeoutError:
                logger.warning(f"Callback query timeout for user {user_id}: {data}")
                self.stats['timeout_queries'] += 1
                try:
                    await query.edit_message_text("⏰ Request timed out. Please try again.")
                except Exception:
                    pass
                return False
                
            except BadRequest as e:
                error_msg = str(e).lower()
                if "query is too old" in error_msg or "query id is invalid" in error_msg:
                    logger.debug(f"Query too old for user {user_id}: {data}")
                    self.stats['timeout_queries'] += 1
                else:
                    logger.error(f"BadRequest in callback query: {e}")
                    self.stats['error_queries'] += 1
                return False
                
            except Exception as e:
                logger.error(f"Error in callback query handler: {e}")
                self.stats['error_queries'] += 1
                try:
                    await query.answer("❌ An error occurred. Please try again.")
                except Exception:
                    pass
                return False
                
            finally:
                # Remove from active queries
                self.active_queries.pop(query_hash, None)
                
        except Exception as e:
            logger.error(f"Critical error in callback query manager: {e}")
            return False
    
    def _is_query_too_old(self, query: CallbackQuery) -> bool:
        """Check if callback query is too old to process"""
        try:
            # Telegram callback queries have a built-in timeout of ~30 seconds
            # We'll be more conservative and reject queries older than our threshold
            current_time = time.time()
            
            # Estimate query age based on current time
            # This is approximate since we don't have the exact query creation time
            estimated_age = 5  # Assume minimum 5 seconds for network latency
            
            return estimated_age > self.max_query_age_seconds
        except Exception:
            return False
    
    def _check_rate_limit(self, user_id: int) -> bool:
        """Check if user is within rate limits"""
        try:
            current_time = datetime.now(timezone.utc)
            
            # Clean up old entries
            if user_id in self.user_last_query:
                last_query_time = self.user_last_query[user_id]
                if (current_time - last_query_time).total_seconds() > self.rate_limit_window:
                    self.user_query_counts[user_id] = 0
            
            # Check current count
            current_count = self.user_query_counts.get(user_id, 0)
            if current_count >= self.max_queries_per_user_per_minute:
                return False
            
            # Update counters
            self.user_query_counts[user_id] = current_count + 1
            self.user_last_query[user_id] = current_time
            
            return True
            
        except Exception as e:
            logger.error(f"Error in rate limit check: {e}")
            return True  # Allow on error
    
    def _determine_priority(self, data: str) -> CallbackPriority:
        """Determine priority of callback query based on data"""
        if data.startswith("admin_"):
            return CallbackPriority.HIGH
        elif data in ["daily_bonus", "verify_channels"]:
            return CallbackPriority.HIGH
        elif data.startswith("approve_") or data.startswith("disapprove_"):
            return CallbackPriority.CRITICAL
        else:
            return CallbackPriority.NORMAL
    
    def _generate_query_hash(self, query_id: str, user_id: int, data: str) -> str:
        """Generate unique hash for query deduplication"""
        content = f"{query_id}:{user_id}:{data}"
        return hashlib.md5(content.encode()).hexdigest()
    
    async def _answer_query_safely(self, query: CallbackQuery):
        """Safely answer callback query to prevent timeout"""
        try:
            # Try to answer immediately to prevent timeout
            await asyncio.wait_for(query.answer(), timeout=5.0)
        except asyncio.TimeoutError:
            logger.debug("Query answer timeout - query likely too old")
        except BadRequest as e:
            error_msg = str(e).lower()
            if "query is too old" in error_msg or "query id is invalid" in error_msg:
                # Query is already too old, skip silently
                logger.debug(f"Query too old to answer: {query.id}")
            else:
                logger.warning(f"BadRequest answering query: {e}")
        except Exception as e:
            logger.debug(f"Failed to answer query: {e}")
    
    def _update_response_time_stats(self, response_time: float):
        """Update average response time statistics"""
        try:
            current_avg = self.stats['average_response_time']
            total_successful = self.stats['successful_queries']
            
            if total_successful == 1:
                self.stats['average_response_time'] = response_time
            else:
                # Calculate running average
                self.stats['average_response_time'] = (
                    (current_avg * (total_successful - 1) + response_time) / total_successful
                )
        except Exception as e:
            logger.error(f"Error updating response time stats: {e}")
    
    async def _cleanup_loop(self):
        """Periodic cleanup of old data"""
        while self._running:
            try:
                await asyncio.sleep(self.rate_limit_cleanup_interval)
                await self._cleanup_old_data()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
    
    async def _cleanup_old_data(self):
        """Clean up old queries and rate limit data"""
        try:
            current_time = datetime.now(timezone.utc)
            
            # Clean up old active queries
            old_queries = []
            for query_hash, query_info in self.active_queries.items():
                age = (current_time - query_info.timestamp).total_seconds()
                if age > self.query_timeout_seconds * 2:  # Double timeout for cleanup
                    old_queries.append(query_hash)
            
            for query_hash in old_queries:
                self.active_queries.pop(query_hash, None)
            
            # Clean up old rate limit data
            old_users = []
            for user_id, last_query_time in self.user_last_query.items():
                age = (current_time - last_query_time).total_seconds()
                if age > self.rate_limit_window * 2:  # Double window for cleanup
                    old_users.append(user_id)
            
            for user_id in old_users:
                self.user_query_counts.pop(user_id, None)
                self.user_last_query.pop(user_id, None)
            
            # Clean up processed queries (keep only recent ones)
            if len(self.processed_queries) > 10000:  # Limit memory usage
                # Keep only the most recent 5000
                recent_queries = list(self.processed_queries)[-5000:]
                self.processed_queries = set(recent_queries)
            
            if old_queries or old_users:
                logger.debug(f"Cleaned up {len(old_queries)} old queries and {len(old_users)} old rate limit entries")
                
        except Exception as e:
            logger.error(f"Error in cleanup: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get callback query statistics"""
        total = self.stats['total_queries']
        success_rate = (self.stats['successful_queries'] / total * 100) if total > 0 else 0
        
        return {
            **self.stats,
            'success_rate': round(success_rate, 2),
            'active_queries': len(self.active_queries),
            'tracked_users': len(self.user_query_counts),
            'processed_queries_cache_size': len(self.processed_queries)
        }

# Global callback query manager instance
callback_query_manager = None

def get_callback_query_manager() -> CallbackQueryManager:
    """Get global callback query manager instance"""
    global callback_query_manager
    if callback_query_manager is None:
        callback_query_manager = CallbackQueryManager()
    return callback_query_manager
