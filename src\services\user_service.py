"""
User service for managing user operations
"""

import logging
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any
from pymongo.errors import DuplicateKeyError

from src.database import Database
from src.models.user import User
from config import Config
from src.services.cache_service import get_cache_service
from src.services.concurrent_operations_manager import get_concurrent_operations_manager, OperationType


logger = logging.getLogger(__name__)

class UserService:
    """Service for user management operations"""
    
    def __init__(self, database: Database):
        self.db = database
        self.cache = get_cache_service()
        self.concurrent_ops = get_concurrent_operations_manager()
    
    async def create_user(self, user_data: Dict[str, Any], referral_code: Optional[str] = None) -> Optional[User]:
        """Create a new user"""
        try:
            logger.info(f"🔄 UserService.create_user called for user {user_data.get('user_id')}")

            # Check if user already exists
            existing_user = await self.get_user(user_data['user_id'])
            if existing_user:
                logger.info(f"👤 User {user_data['user_id']} already exists, returning existing user")
                return existing_user

            logger.info(f"🆕 Creating new user {user_data['user_id']}")

            # Create user object with default notification setting
            from config import Config
            user = User(
                user_id=user_data['user_id'],
                username=user_data.get('username'),
                first_name=user_data.get('first_name'),
                last_name=user_data.get('last_name'),
                language_code=user_data.get('language_code'),
                is_bot=user_data.get('is_bot', False),
                is_premium=user_data.get('is_premium', False),
                daily_notifications=Config.DEFAULT_DAILY_NOTIFICATIONS
            )

            # Apply welcome bonus from admin settings
            try:
                from src.services.admin_settings_service import AdminSettingsService
                admin_settings_service = AdminSettingsService(self.db.db)
                welcome_bonus = await admin_settings_service.get_setting_value('welcome_bonus', 0)

                if welcome_bonus > 0:
                    user.add_balance(welcome_bonus, "Welcome bonus")
                    logger.info(f"Applied welcome bonus of 💎{welcome_bonus} to new user {user.user_id}")
            except Exception as e:
                logger.error(f"Failed to apply welcome bonus to user {user.user_id}: {e}")

            logger.info(f"📋 User object created: {user.user_id}")

            # Set referrer if referral code provided
            if referral_code:
                logger.info(f"🔗 Processing referral code: {referral_code}")
                referrer = await self.get_user_by_referral_code(referral_code)
                if referrer and referrer.user_id != user.user_id:
                    user.referred_by = referrer.user_id
                    logger.info(f"✅ Set referrer: {referrer.user_id} for user {user.user_id}")
                else:
                    logger.info(f"❌ No valid referrer found for code: {referral_code}")

            # Save to database
            logger.info(f"💾 Saving user {user.user_id} to database...")
            user_dict = user.to_dict()
            logger.info(f"📊 User dict: {user_dict}")

            result = await self.db.db.users.insert_one(user_dict)
            logger.info(f"✅ Database insert result: {result.inserted_id}")
            logger.info(f"✅ Created new user: {user.user_id}")

            return user
            
        except DuplicateKeyError:
            # User already exists, return existing user
            return await self.get_user(user_data['user_id'])
        except Exception as e:
            logger.error(f"Failed to create user {user_data.get('user_id')}: {e}")
            return None
    
    async def get_user(self, user_id: int) -> Optional[User]:
        """Get user by ID with caching for high performance"""
        try:
            # Try cache first
            cached_data = await self.cache.get_user_data(user_id)
            if cached_data:
                return User.from_dict(cached_data)

            # Cache miss - get from database
            user_data = await self.db.db.users.find_one({'user_id': user_id})
            if user_data is not None:
                # Cache the result for future requests
                await self.cache.set_user_data(user_id, user_data)
                return User.from_dict(user_data)
            return None
        except Exception as e:
            logger.error(f"Failed to get user {user_id}: {e}")
            return None

    async def get_user_by_referral_code(self, referral_code: str) -> Optional[User]:
        """Get user by referral code"""
        try:
            user_data = await self.db.db.users.find_one({'referral_code': referral_code})
            if user_data is not None:
                return User.from_dict(user_data)
            return None
        except Exception as e:
            logger.error(f"Failed to get user by referral code {referral_code}: {e}")
            return None

    async def update_user(self, user: User) -> bool:
        """Update user in database and invalidate cache"""
        try:
            user.update_activity()
            result = await self.db.db.users.update_one(
                {'user_id': user.user_id},
                {'$set': user.to_dict()}
            )

            # Invalidate cache after successful update
            if result.modified_count > 0:
                await self.cache.invalidate_user_cache(user.user_id)

            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Failed to update user {user.user_id}: {e}")
            return False
    
    async def update_user_balance(self, user_id: int, amount: float, reason: str = "") -> bool:
        """Update user balance"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return False
            
            if amount > 0:
                user.add_balance(amount, reason)
            else:
                if not user.deduct_balance(abs(amount)):
                    return False
            
            result = await self.update_user(user)
            
            if result:return result
            
        except Exception as e:
            logger.error(f"Failed to update balance for user {user_id}: {e}")
            return False
    
    async def ban_user(self, user_id: int, reason: str = "", admin_id: Optional[int] = None) -> bool:
        """Ban a user"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return False
            
            user.ban_user(reason)
            result = await self.update_user(user)
            
            if result:return result
            
        except Exception as e:
            logger.error(f"Failed to ban user {user_id}: {e}")
            return False
    
    async def unban_user(self, user_id: int, admin_id: Optional[int] = None) -> bool:
        """Unban a user"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return False
            
            user.unban_user()
            result = await self.update_user(user)
            
            if result:return result
            
        except Exception as e:
            logger.error(f"Failed to unban user {user_id}: {e}")
            return False
    
    async def get_users_count(self) -> int:
        """Get total number of users"""
        try:
            return await self.db.db.users.count_documents({})
        except Exception as e:
            logger.error(f"Failed to get users count: {e}")
            return 0

    async def get_active_users_count(self) -> int:
        """Get number of active users"""
        try:
            return await self.db.db.users.count_documents({'is_active': True, 'is_banned': False})
        except Exception as e:
            logger.error(f"Failed to get active users count: {e}")
            return 0

    async def get_users_paginated(self, page: int = 0, per_page: int = 20) -> List[User]:
        """Get users with pagination"""
        try:
            skip = page * per_page
            cursor = self.db.db.users.find().sort('created_at', -1).skip(skip).limit(per_page)
            users = []

            async for user_data in cursor:
                users.append(User.from_dict(user_data))

            return users

        except Exception as e:
            logger.error(f"Failed to get paginated users: {e}")
            return []
    
    async def search_users(self, query: str, limit: int = 20) -> List[User]:
        """Search users by username, first name, or user ID"""
        try:
            # Try to search by user ID if query is numeric
            search_filters = []

            if query.isdigit():
                search_filters.append({'user_id': int(query)})

            # Search by username and name
            search_filters.extend([
                {'username': {'$regex': query, '$options': 'i'}},
                {'first_name': {'$regex': query, '$options': 'i'}},
                {'last_name': {'$regex': query, '$options': 'i'}}
            ])

            cursor = self.db.db.users.find({'$or': search_filters}).limit(limit)
            users = []

            async for user_data in cursor:
                users.append(User.from_dict(user_data))

            return users

        except Exception as e:
            logger.error(f"Failed to search users: {e}")
            return []

    async def cleanup_incomplete_registrations(self, max_age_minutes: int = 30) -> int:
        """Remove users who didn't complete registration within the specified time"""
        try:
            from datetime import datetime, timezone, timedelta

            # Calculate cutoff time
            cutoff_time = datetime.now(timezone.utc) - timedelta(minutes=max_age_minutes)

            # Find incomplete registrations older than cutoff
            query = {
                'registration_completed': {'$ne': True},  # Not completed or field doesn't exist
                'created_at': {'$lt': cutoff_time}
            }

            # Count documents to be deleted
            count = await self.db.db.users.count_documents(query)

            if count > 0:
                # Delete incomplete registrations
                result = await self.db.db.users.delete_many(query)
                logger.info(f"Cleaned up {result.deleted_count} incomplete user registrations")
                return result.deleted_count

            return 0

        except Exception as e:
            logger.error(f"Failed to cleanup incomplete registrations: {e}")
            return 0

    async def mark_registration_completed(self, user_id: int) -> bool:
        """Mark user registration as completed"""
        try:
            user = await self.get_user(user_id)
            if user:
                user.complete_registration()
                return await self.update_user(user)
            return False
        except Exception as e:
            logger.error(f"Failed to mark registration completed for user {user_id}: {e}")
            return False
    
    async def get_top_referrers(self, limit: int = 10) -> List[User]:
        """Get top users by referral count"""
        try:
            cursor = self.db.db.users.find().sort('referral_count', -1).limit(limit)
            users = []
            
            async for user_data in cursor:
                users.append(User.from_dict(user_data))
            
            return users
            
        except Exception as e:
            logger.error(f"Failed to get top referrers: {e}")
            return []

    async def update_user_channel_status(self, user_id: int, channel_id: str, joined: bool) -> bool:
        """Update user's channel join status"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return False
            
            if joined:
                user.join_channel(channel_id)
            else:
                user.leave_channel(channel_id)
            
            return await self.update_user(user)
            
        except Exception as e:
            logger.error(f"Failed to update channel status for user {user_id}: {e}")
            return False
    
    async def get_user_statistics(self, user_id: int) -> Dict[str, Any]:
        """Get comprehensive user statistics"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return {}
            
            # Get referral count
            referral_count = await self.db.db.referrals.count_documents({'referrer_id': user_id})

            # Get transaction count
            transaction_count = await self.db.db.transactions.count_documents({'user_id': user_id})

            # Get withdrawal count
            withdrawal_count = await self.db.db.withdrawals.count_documents({'user_id': user_id})
            
            return {
                'user': user.to_dict(),
                'referral_count': referral_count,
                'transaction_count': transaction_count,
                'withdrawal_count': withdrawal_count,
                'days_since_join': (datetime.now(timezone.utc) - user.created_at).days,
                'can_withdraw': user.balance >= Config.MINIMUM_WITHDRAWAL
            }
            
        except Exception as e:
            logger.error(f"Failed to get user statistics for {user_id}: {e}")
            return {}

    async def add_balance(self, user_id: int, amount: float) -> bool:
        """Add balance to user account"""
        try:
            user = await self.get_user(user_id)
            if user:
                user.add_balance(amount, "Balance addition")
                return await self.update_user(user)
            return False
        except Exception as e:
            logger.error(f"Failed to add balance to user {user_id}: {e}")
            return False

    async def update_last_daily_claim(self, user_id: int) -> bool:
        """Update user's last daily claim time"""
        try:
            user = await self.get_user(user_id)
            if user:
                user.last_daily_bonus = datetime.now(timezone.utc)
                return await self.update_user(user)
            return False
        except Exception as e:
            logger.error(f"Failed to update last daily claim for user {user_id}: {e}")
            return False

    async def get_user_concurrent(self, user_id: int) -> Optional[User]:
        """Get user with concurrent operation optimization"""
        return await self.concurrent_ops.execute_profile_access(
            user_id, OperationType.PROFILE_ACCESS, self.get_user, user_id
        )

    async def update_user_concurrent(self, user: User) -> bool:
        """Update user with concurrent operation safety"""
        return await self.concurrent_ops.execute_operation(
            user.user_id, OperationType.USER_UPDATE, self._update_user_internal, user
        )

    async def add_balance_concurrent(self, user_id: int, amount: float, description: str = "") -> bool:
        """Add balance with concurrent operation safety"""
        return await self.concurrent_ops.execute_operation(
            user_id, OperationType.BALANCE_UPDATE, self._add_balance_internal,
            user_id, amount, description, priority=2  # Higher priority for balance operations
        )

    async def claim_daily_bonus_concurrent(self, user_id: int, bonus_amount: float) -> bool:
        """Claim daily bonus with user-level locking to prevent double claims"""
        return await self.concurrent_ops.execute_daily_bonus_operation(
            user_id, self._claim_daily_bonus_internal, user_id, bonus_amount
        )

    async def process_referral_concurrent(self, referrer_id: int, referred_id: int, reward_amount: float) -> bool:
        """Process referral with concurrent operation safety"""
        return await self.concurrent_ops.execute_referral_operation(
            referrer_id, self._process_referral_internal, referrer_id, referred_id, reward_amount
        )

    async def batch_get_users(self, user_ids: List[int]) -> List[Optional[User]]:
        """Get multiple users concurrently with caching"""
        operations = []
        for user_id in user_ids:
            operations.append({
                'type': OperationType.PROFILE_ACCESS,
                'function': self.get_user,
                'args': (user_id,),
                'kwargs': {}
            })

        results = await self.concurrent_ops.batch_execute_operations(operations)
        return results

    # Internal methods for concurrent operations
    async def _update_user_internal(self, user: User) -> bool:
        """Internal method for updating user (used by concurrent operations)"""
        try:
            user.update_activity()
            result = await self.db.db.users.update_one(
                {'user_id': user.user_id},
                {'$set': user.to_dict()}
            )

            if result.modified_count > 0:
                await self.cache.invalidate_user_cache(user.user_id)
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to update user {user.user_id}: {e}")
            return False

    async def _add_balance_internal(self, user_id: int, amount: float, description: str = "") -> bool:
        """Internal method for adding balance (used by concurrent operations)"""
        try:
            user = await self.get_user(user_id)
            if user:
                user.add_balance(amount, description)
                return await self._update_user_internal(user)
            return False
        except Exception as e:
            logger.error(f"Failed to add balance to user {user_id}: {e}")
            return False

    async def _claim_daily_bonus_internal(self, user_id: int, bonus_amount: float) -> bool:
        """Internal method for claiming daily bonus (used by concurrent operations)"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return False

            # Double-check if user can claim (prevent race conditions)
            if not user.can_claim_daily_bonus():
                logger.warning(f"User {user_id} attempted to claim daily bonus but not eligible")
                return False

            # Claim bonus and update
            user.claim_daily_bonus(bonus_amount)
            success = await self._update_user_internal(user)

            if success:
                # Invalidate daily bonus cache
                await self.cache.delete(f"daily_bonus:{user_id}")
                logger.info(f"User {user_id} successfully claimed daily bonus of 💎{bonus_amount}")

            return success
        except Exception as e:
            logger.error(f"Failed to claim daily bonus for user {user_id}: {e}")
            return False

    async def _process_referral_internal(self, referrer_id: int, referred_id: int, reward_amount: float) -> bool:
        """Internal method for processing referral (used by concurrent operations)"""
        try:
            referrer = await self.get_user(referrer_id)
            if not referrer:
                return False

            # Add referral reward
            referrer.add_balance(reward_amount, f"Referral bonus for user {referred_id}")
            success = await self._update_user_internal(referrer)

            if success:
                # Invalidate referral cache
                await self.cache.delete(f"referral_data:{referrer_id}")
                logger.info(f"Processed referral: {referrer_id} earned 💎{reward_amount} for referring {referred_id}")

            return success
        except Exception as e:
            logger.error(f"Failed to process referral for {referrer_id}: {e}")
            return False
