"""
Resilient Database Connection Manager
Handles DNS timeouts, connection failures, and provides graceful degradation
"""

import asyncio
import logging
import time
from typing import Optional, Dict, Any, List
from datetime import datetime, timezone, timedelta
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError, NetworkTimeout
import dns.resolver

from config import Config
from src.utils.network_diagnostics import network_diagnostics

logger = logging.getLogger(__name__)

class ConnectionState:
    """Track database connection state"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    DEGRADED = "degraded"
    FAILED = "failed"

class ResilientDatabase:
    """Resilient database connection with retry logic and graceful degradation"""
    
    def __init__(self):
        self.client: Optional[AsyncIOMotorClient] = None
        self.db = None
        self.state = ConnectionState.DISCONNECTED
        self.last_connection_attempt = None
        self.connection_failures = 0
        self.max_retries = 3  # Reduced for faster startup
        self.retry_delays = [0.5, 1, 2]  # Faster progressive delays in seconds
        self.degraded_mode = False
        self.connection_history = []
        self.last_connection_error = None
        
        # Alternative DNS servers for fallback
        self.dns_servers = ['8.8.8.8', '1.1.1.1', '208.67.222.222']
        self.current_dns_index = 0
        
        # Connection monitoring
        self.last_successful_ping = None
        self.ping_failures = 0
        self.max_ping_failures = 3
        
        # Retry task
        self._retry_task = None
        self._monitoring_task = None
        self._running = False
    
    async def connect_with_resilience(self) -> bool:
        """Connect to MongoDB with comprehensive resilience and diagnostics"""
        logger.info("🔄 Starting resilient database connection...")

        self.state = ConnectionState.CONNECTING
        self.last_connection_attempt = datetime.now(timezone.utc)

        # First, run network diagnostics
        await self._run_connection_diagnostics()

        # Attempt connection with multiple strategies
        connection_strategies = [
            self._connect_with_default_settings,
            self._connect_with_extended_timeouts,
            self._connect_with_alternative_dns,
            self._connect_with_minimal_settings
        ]

        for strategy_index, strategy in enumerate(connection_strategies):
            logger.info(f"🔄 Trying connection strategy {strategy_index + 1}/{len(connection_strategies)}")

            try:
                success = await strategy()
                if success:
                    self.state = ConnectionState.CONNECTED
                    self.connection_failures = 0
                    self._record_connection_success()

                    # Start monitoring
                    await self._start_monitoring()

                    logger.info("✅ Database connected successfully with resilient connection")
                    return True

            except Exception as e:
                self.last_connection_error = e
                logger.warning(f"Connection strategy {strategy_index + 1} failed: {e}")
                continue
        
        # First, run network diagnostics
        await self._run_connection_diagnostics()
        
        # Attempt connection with multiple strategies
        connection_strategies = [
            self._connect_with_default_settings,
            self._connect_with_extended_timeouts,
            self._connect_with_alternative_dns,
            self._connect_with_minimal_settings
        ]
        
        for strategy_index, strategy in enumerate(connection_strategies):
            logger.info(f"🔄 Trying connection strategy {strategy_index + 1}/{len(connection_strategies)}")
            
            try:
                success = await strategy()
                if success:
                    self.state = ConnectionState.CONNECTED
                    self.connection_failures = 0
                    self._record_connection_success()
                    
                    # Start monitoring
                    await self._start_monitoring()
                    
                    logger.info("✅ Database connected successfully with resilient connection")
                    return True
                    
            except Exception as e:
                self.last_connection_error = e
                logger.warning(f"Connection strategy {strategy_index + 1} failed: {e}")
                continue
        
        # All strategies failed
        self.connection_failures += 1
        self.state = ConnectionState.FAILED
        self._record_connection_failure()

        # Check for DNS timeout errors and immediately go to degraded mode
        if "resolution lifetime expired" in str(self.last_connection_error) or "DNS operation timed out" in str(self.last_connection_error):
            logger.warning("🔄 DNS timeout detected - immediately starting in degraded mode")
            self.degraded_mode = True
            self.state = ConnectionState.DEGRADED
            # Start retry mechanism in background
            asyncio.create_task(self._start_retry_mechanism())
            return False  # Return False but don't crash the bot

        # Start retry mechanism
        await self._start_retry_mechanism()

        # Check if we should start in degraded mode
        if self.connection_failures >= 2:  # Reduced from 3 to 2
            logger.warning("🔄 Starting in degraded mode due to persistent connection failures")
            self.degraded_mode = True
            self.state = ConnectionState.DEGRADED
            return False  # Return False but don't crash the bot

        return False
    
    async def _run_connection_diagnostics(self):
        """Run comprehensive network diagnostics"""
        try:
            logger.info("🔍 Running network diagnostics...")
            diagnosis = await network_diagnostics.diagnose_connection_issue(Config.MONGODB_URI)
            
            # Log key findings
            dns_status = diagnosis.get('dns_resolution', {}).get('status', 'unknown')
            network_status = diagnosis.get('network_connectivity', {}).get('status', 'unknown')
            mongodb_status = diagnosis.get('mongodb_specific', {}).get('connected', False)
            
            logger.info(f"📊 Diagnostics Results:")
            logger.info(f"   - DNS Resolution: {dns_status}")
            logger.info(f"   - Network Connectivity: {network_status}")
            logger.info(f"   - MongoDB Connectivity: {mongodb_status}")
            
            # Log recommendations
            recommendations = diagnosis.get('recommendations', [])
            if recommendations:
                logger.info("💡 Troubleshooting Recommendations:")
                for rec in recommendations[:5]:  # Show first 5 recommendations
                    logger.info(f"   {rec}")
            
            return diagnosis
            
        except Exception as e:
            logger.error(f"Failed to run diagnostics: {e}")
            return None
    
    async def _connect_with_default_settings(self) -> bool:
        """Connect with default optimized settings"""
        try:
            logger.info("🔄 Attempting connection with default settings...")
            
            self.client = AsyncIOMotorClient(
                Config.MONGODB_URI,
                maxPoolSize=20,  # Reduced for initial connection
                minPoolSize=2,
                serverSelectionTimeoutMS=15000,  # 15 seconds
                connectTimeoutMS=15000,
                socketTimeoutMS=15000,
                retryWrites=True,
                retryReads=True
            )
            
            # Test connection
            await asyncio.wait_for(self.client.admin.command('ping'), timeout=20)
            self.db = self.client[Config.DATABASE_NAME]
            
            return True
            
        except Exception as e:
            logger.debug(f"Default connection failed: {e}")
            if self.client:
                self.client.close()
                self.client = None
            return False
    
    async def _connect_with_extended_timeouts(self) -> bool:
        """Connect with extended timeout settings"""
        try:
            logger.info("🔄 Attempting connection with extended timeouts...")
            
            self.client = AsyncIOMotorClient(
                Config.MONGODB_URI,
                maxPoolSize=10,
                minPoolSize=1,
                serverSelectionTimeoutMS=30000,  # 30 seconds
                connectTimeoutMS=30000,
                socketTimeoutMS=30000,
                retryWrites=True,
                retryReads=True,
                heartbeatFrequencyMS=30000  # 30 seconds heartbeat
            )
            
            # Test connection with longer timeout
            await asyncio.wait_for(self.client.admin.command('ping'), timeout=35)
            self.db = self.client[Config.DATABASE_NAME]
            
            return True
            
        except Exception as e:
            logger.debug(f"Extended timeout connection failed: {e}")
            if self.client:
                self.client.close()
                self.client = None
            return False
    
    async def _connect_with_alternative_dns(self) -> bool:
        """Connect using alternative DNS servers"""
        try:
            logger.info("🔄 Attempting connection with alternative DNS...")
            
            # Configure alternative DNS
            await self._configure_alternative_dns()
            
            self.client = AsyncIOMotorClient(
                Config.MONGODB_URI,
                maxPoolSize=10,
                minPoolSize=1,
                serverSelectionTimeoutMS=20000,
                connectTimeoutMS=20000,
                socketTimeoutMS=20000,
                retryWrites=True
            )
            
            # Test connection
            await asyncio.wait_for(self.client.admin.command('ping'), timeout=25)
            self.db = self.client[Config.DATABASE_NAME]
            
            return True
            
        except Exception as e:
            logger.debug(f"Alternative DNS connection failed: {e}")
            if self.client:
                self.client.close()
                self.client = None
            return False
    
    async def _connect_with_minimal_settings(self) -> bool:
        """Connect with minimal settings as last resort"""
        try:
            logger.info("🔄 Attempting connection with minimal settings...")
            
            self.client = AsyncIOMotorClient(
                Config.MONGODB_URI,
                maxPoolSize=5,
                serverSelectionTimeoutMS=45000,  # 45 seconds - very generous
                connectTimeoutMS=45000,
                socketTimeoutMS=45000
            )
            
            # Test connection with very long timeout
            await asyncio.wait_for(self.client.admin.command('ping'), timeout=50)
            self.db = self.client[Config.DATABASE_NAME]
            
            return True
            
        except Exception as e:
            logger.debug(f"Minimal settings connection failed: {e}")
            if self.client:
                self.client.close()
                self.client = None
            return False
    
    async def _configure_alternative_dns(self):
        """Configure alternative DNS servers"""
        try:
            if self.current_dns_index < len(self.dns_servers):
                dns_server = self.dns_servers[self.current_dns_index]
                logger.info(f"🔄 Configuring DNS server: {dns_server}")
                
                # Configure DNS resolver
                resolver = dns.resolver.Resolver()
                resolver.nameservers = [dns_server]
                dns.resolver.default_resolver = resolver
                
                self.current_dns_index += 1
                
        except Exception as e:
            logger.warning(f"Failed to configure alternative DNS: {e}")
    
    async def _start_monitoring(self):
        """Start connection monitoring"""
        if not self._running:
            self._running = True
            self._monitoring_task = asyncio.create_task(self._monitor_connection())
    
    async def _monitor_connection(self):
        """Monitor database connection health"""
        while self._running and self.state == ConnectionState.CONNECTED:
            try:
                await asyncio.sleep(30)  # Check every 30 seconds
                
                # Ping database
                await asyncio.wait_for(self.client.admin.command('ping'), timeout=10)
                self.last_successful_ping = datetime.now(timezone.utc)
                self.ping_failures = 0
                
            except Exception as e:
                self.ping_failures += 1
                logger.warning(f"Database ping failed ({self.ping_failures}/{self.max_ping_failures}): {e}")
                
                if self.ping_failures >= self.max_ping_failures:
                    logger.error("Database connection lost - starting reconnection")
                    self.state = ConnectionState.FAILED
                    await self._start_retry_mechanism()
                    break
    
    async def _start_retry_mechanism(self):
        """Start automatic retry mechanism"""
        if self._retry_task and not self._retry_task.done():
            return  # Already retrying
        
        self._retry_task = asyncio.create_task(self._retry_connection())
    
    async def _retry_connection(self):
        """Retry connection with progressive delays"""
        retry_count = 0
        
        while retry_count < self.max_retries and self.state in [ConnectionState.FAILED, ConnectionState.DEGRADED]:
            delay = self.retry_delays[min(retry_count, len(self.retry_delays) - 1)]
            logger.info(f"🔄 Retrying database connection in {delay} seconds (attempt {retry_count + 1}/{self.max_retries})")
            
            await asyncio.sleep(delay)
            
            try:
                success = await self.connect_with_resilience()
                if success:
                    logger.info("✅ Database reconnection successful")
                    return
                    
            except Exception as e:
                logger.error(f"Retry attempt {retry_count + 1} failed: {e}")
            
            retry_count += 1
        
        # All retries failed
        logger.error("❌ All database connection retries failed - entering permanent degraded mode")
        self.degraded_mode = True
        self.state = ConnectionState.DEGRADED
    
    def _record_connection_success(self):
        """Record successful connection"""
        self.connection_history.append({
            'timestamp': datetime.now(timezone.utc),
            'status': 'success',
            'failures_before': self.connection_failures
        })
        
        # Keep only last 10 records
        if len(self.connection_history) > 10:
            self.connection_history = self.connection_history[-10:]
    
    def _record_connection_failure(self):
        """Record connection failure"""
        self.connection_history.append({
            'timestamp': datetime.now(timezone.utc),
            'status': 'failure',
            'failure_count': self.connection_failures
        })
        
        # Keep only last 10 records
        if len(self.connection_history) > 10:
            self.connection_history = self.connection_history[-10:]
    
    async def health_check(self) -> Dict[str, Any]:
        """Get database health status"""
        try:
            if self.state == ConnectionState.CONNECTED and self.client:
                start_time = time.time()
                await asyncio.wait_for(self.client.admin.command('ping'), timeout=5)
                response_time = (time.time() - start_time) * 1000
                
                return {
                    'status': 'healthy',
                    'state': self.state,
                    'response_time_ms': response_time,
                    'degraded_mode': self.degraded_mode,
                    'connection_failures': self.connection_failures,
                    'last_successful_ping': self.last_successful_ping.isoformat() if self.last_successful_ping else None
                }
            else:
                return {
                    'status': 'unhealthy',
                    'state': self.state,
                    'degraded_mode': self.degraded_mode,
                    'connection_failures': self.connection_failures,
                    'last_connection_attempt': self.last_connection_attempt.isoformat() if self.last_connection_attempt else None
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'state': self.state,
                'error': str(e),
                'degraded_mode': self.degraded_mode
            }
    
    def is_connected(self) -> bool:
        """Check if database is connected"""
        return self.state == ConnectionState.CONNECTED and self.client is not None
    
    def is_degraded(self) -> bool:
        """Check if running in degraded mode"""
        return self.degraded_mode
    
    async def close(self):
        """Close database connection"""
        self._running = False
        
        if self._monitoring_task:
            self._monitoring_task.cancel()
        
        if self._retry_task:
            self._retry_task.cancel()
        
        if self.client:
            self.client.close()
            self.client = None
        
        self.state = ConnectionState.DISCONNECTED
        logger.info("Database connection closed")

# Global resilient database instance
resilient_db = None

def get_resilient_database() -> ResilientDatabase:
    """Get global resilient database instance"""
    global resilient_db
    if resilient_db is None:
        resilient_db = ResilientDatabase()
    return resilient_db
