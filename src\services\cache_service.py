"""
High-Performance Caching Service for Telegram Bot Scalability
Supports both in-memory and Redis caching for lakhs of users
"""

import asyncio
import json
import logging
import time
from typing import Any, Dict, Optional, Union
from datetime import datetime, timedelta
from dataclasses import asdict

try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

from config import Config

logger = logging.getLogger(__name__)

class CacheService:
    """High-performance caching service with fallback to in-memory cache"""
    
    def __init__(self, redis_url: Optional[str] = None):
        self.redis_client = None
        self.memory_cache: Dict[str, Dict] = {}
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0
        }
        
        # Cache TTL settings (in seconds)
        self.default_ttl = 900  # 15 minutes
        self.ttl_settings = {
            'user_data': 900,      # 15 minutes
            'admin_settings': 300,  # 5 minutes
            'channel_status': 3600, # 1 hour
            'daily_bonus': 86400,   # 24 hours
            'referral_data': 1800,  # 30 minutes
            'task_data': 600,       # 10 minutes
        }
        
        # Initialize Redis if available
        if REDIS_AVAILABLE and redis_url:
            self._init_redis(redis_url)
        else:
            logger.info("Using in-memory cache (Redis not available)")
    
    def _init_redis(self, redis_url: str):
        """Initialize Redis connection"""
        try:
            self.redis_client = redis.from_url(
                redis_url,
                encoding='utf-8',
                decode_responses=True,
                max_connections=50,
                retry_on_timeout=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            logger.info("Redis cache initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Redis: {e}")
            self.redis_client = None
    
    async def get(self, key: str, cache_type: str = 'default') -> Optional[Any]:
        """Get value from cache with automatic deserialization"""
        try:
            # Try Redis first
            if self.redis_client:
                try:
                    value = await self.redis_client.get(key)
                    if value is not None:
                        self.cache_stats['hits'] += 1
                        return json.loads(value)
                except Exception as e:
                    logger.debug(f"Redis get failed for key {key}: {e}")
            
            # Fallback to memory cache
            if key in self.memory_cache:
                cache_entry = self.memory_cache[key]
                if cache_entry['expires_at'] > time.time():
                    self.cache_stats['hits'] += 1
                    return cache_entry['value']
                else:
                    # Expired entry
                    del self.memory_cache[key]
            
            self.cache_stats['misses'] += 1
            return None
            
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            self.cache_stats['misses'] += 1
            return None
    
    async def set(self, key: str, value: Any, cache_type: str = 'default', ttl: Optional[int] = None) -> bool:
        """Set value in cache with automatic serialization"""
        try:
            ttl = ttl or self.ttl_settings.get(cache_type, self.default_ttl)
            serialized_value = json.dumps(value, default=self._json_serializer)
            
            # Try Redis first
            if self.redis_client:
                try:
                    await self.redis_client.setex(key, ttl, serialized_value)
                    self.cache_stats['sets'] += 1
                    return True
                except Exception as e:
                    logger.debug(f"Redis set failed for key {key}: {e}")
            
            # Fallback to memory cache
            self.memory_cache[key] = {
                'value': value,
                'expires_at': time.time() + ttl
            }
            
            # Cleanup memory cache if it gets too large
            if len(self.memory_cache) > 10000:
                await self._cleanup_memory_cache()
            
            self.cache_stats['sets'] += 1
            return True
            
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete value from cache"""
        try:
            deleted = False
            
            # Delete from Redis
            if self.redis_client:
                try:
                    result = await self.redis_client.delete(key)
                    deleted = result > 0
                except Exception as e:
                    logger.debug(f"Redis delete failed for key {key}: {e}")
            
            # Delete from memory cache
            if key in self.memory_cache:
                del self.memory_cache[key]
                deleted = True
            
            if deleted:
                self.cache_stats['deletes'] += 1
            
            return deleted
            
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            return False
    
    async def get_user_data(self, user_id: int) -> Optional[Dict]:
        """Get cached user data"""
        return await self.get(f"user:{user_id}", 'user_data')
    
    async def set_user_data(self, user_id: int, user_data: Dict) -> bool:
        """Cache user data"""
        return await self.set(f"user:{user_id}", user_data, 'user_data')
    
    async def get_admin_settings(self) -> Optional[Dict]:
        """Get cached admin settings"""
        return await self.get("admin_settings", 'admin_settings')
    
    async def set_admin_settings(self, settings: Dict) -> bool:
        """Cache admin settings"""
        return await self.set("admin_settings", settings, 'admin_settings')
    
    async def get_channel_status(self, user_id: int, channel_id: str) -> Optional[bool]:
        """Get cached channel membership status"""
        result = await self.get(f"channel:{user_id}:{channel_id}", 'channel_status')
        return result
    
    async def set_channel_status(self, user_id: int, channel_id: str, is_member: bool) -> bool:
        """Cache channel membership status"""
        return await self.set(f"channel:{user_id}:{channel_id}", is_member, 'channel_status')
    
    async def get_daily_bonus_status(self, user_id: int) -> Optional[Dict]:
        """Get cached daily bonus status"""
        return await self.get(f"daily_bonus:{user_id}", 'daily_bonus')
    
    async def set_daily_bonus_status(self, user_id: int, status: Dict) -> bool:
        """Cache daily bonus status"""
        return await self.set(f"daily_bonus:{user_id}", status, 'daily_bonus')
    
    async def invalidate_user_cache(self, user_id: int):
        """Invalidate all cache entries for a user"""
        patterns = [
            f"user:{user_id}",
            f"daily_bonus:{user_id}",
            f"channel:{user_id}:*"
        ]
        
        for pattern in patterns:
            if '*' in pattern:
                # For patterns with wildcards, we need to find matching keys
                if self.redis_client:
                    try:
                        keys = await self.redis_client.keys(pattern)
                        if keys:
                            await self.redis_client.delete(*keys)
                    except Exception as e:
                        logger.debug(f"Redis pattern delete failed: {e}")
                
                # For memory cache, iterate through keys
                keys_to_delete = [k for k in self.memory_cache.keys() if k.startswith(pattern.replace('*', ''))]
                for key in keys_to_delete:
                    del self.memory_cache[key]
            else:
                await self.delete(pattern)
    
    async def get_cache_stats(self) -> Dict:
        """Get cache performance statistics"""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = (self.cache_stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        stats = {
            'hits': self.cache_stats['hits'],
            'misses': self.cache_stats['misses'],
            'sets': self.cache_stats['sets'],
            'deletes': self.cache_stats['deletes'],
            'hit_rate': round(hit_rate, 2),
            'memory_cache_size': len(self.memory_cache),
            'redis_available': self.redis_client is not None
        }
        
        return stats
    
    async def _cleanup_memory_cache(self):
        """Clean up expired entries from memory cache"""
        current_time = time.time()
        expired_keys = [
            key for key, entry in self.memory_cache.items()
            if entry['expires_at'] <= current_time
        ]
        
        for key in expired_keys:
            del self.memory_cache[key]
        
        logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
    
    def _json_serializer(self, obj):
        """Custom JSON serializer for complex objects"""
        if hasattr(obj, 'to_dict'):
            return obj.to_dict()
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        elif isinstance(obj, datetime):
            return obj.isoformat()
        else:
            return str(obj)
    
    async def close(self):
        """Close cache connections"""
        if self.redis_client:
            await self.redis_client.close()
        self.memory_cache.clear()

# Global cache instance
cache_service = None

def get_cache_service() -> CacheService:
    """Get global cache service instance"""
    global cache_service
    if cache_service is None:
        redis_url = getattr(Config, 'REDIS_URL', None)
        cache_service = CacheService(redis_url)
    return cache_service
